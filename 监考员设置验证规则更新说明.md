# 监考员设置验证规则更新说明

## 更新概述

根据用户要求，更新了向导数据验证规则中的监考员设置验证规则。

## 验证规则变更

### 删除的规则

1. **删除规则1**：同一个监考员必监考科目的数量加场次限制的总和不能超过考试所有科目的总和
   - **原代码**：`if required_count + session_limit > total_subjects_count`
   - **删除原因**：用户要求删除此规则

2. **删除规则2**：同一个监考员必监考科目的数量和不监考科目的数量不能超过考试所有科目的总和
   - **原代码**：`if required_count + unavailable_count > total_subjects_count`
   - **删除原因**：用户要求删除此规则

### 新增的规则

1. **新增规则1**：同一个监考员必监考科目的数量+不监考科目的数量之和不能超过考试所有科目的总和
   - **新代码**：`if required_count + unavailable_count > total_subjects_count`
   - **错误信息**：`必监考科目数量({required_count})+不监考科目数量({unavailable_count})之和({required_count + unavailable_count})不能超过考试所有科目总和({total_subjects_count})`

2. **新增规则2**：同一个监考员必监考科目的数量小于等于场次限制的数量
   - **新代码**：`if required_count > session_limit`
   - **错误信息**：`必监考科目数量({required_count})不能大于场次限制数量({session_limit})`

## 修改的文件

### 1. `extended_validator.py`

**修改位置**：第621-631行的 `_validate_proctor_settings` 方法

**修改前**：
```python
# 检查必监考科目数量加场次限制不能超过考试所有科目总和
required_count = len(required_subjects)
if required_count + session_limit > total_subjects_count:
    self.errors.append(f"监考员设置表第{row_num}行（{proctor_name}）必监考科目数量({required_count})加场次限制({session_limit})不能超过考试所有科目总和({total_subjects_count})")
    self.strict_validation_failed = True

# 检查必监考科目和不监考科目数量不能超过考试所有科目总和
unavailable_count = len(unavailable_subjects)
if required_count + unavailable_count > total_subjects_count:
    self.errors.append(f"监考员设置表第{row_num}行（{proctor_name}）必监考科目数量({required_count})和不监考科目数量({unavailable_count})不能超过考试所有科目总和({total_subjects_count})")
    self.strict_validation_failed = True
```

**修改后**：
```python
# 新的验证规则：必监考科目数量+不监考科目数量之和不能超过考试所有科目总和
required_count = len(required_subjects)
unavailable_count = len(unavailable_subjects)
if required_count + unavailable_count > total_subjects_count:
    self.errors.append(f"监考员设置表第{row_num}行（{proctor_name}）必监考科目数量({required_count})+不监考科目数量({unavailable_count})之和({required_count + unavailable_count})不能超过考试所有科目总和({total_subjects_count})")
    self.strict_validation_failed = True

# 新的验证规则：必监考科目数量小于等于场次限制数量
if required_count > session_limit:
    self.errors.append(f"监考员设置表第{row_num}行（{proctor_name}）必监考科目数量({required_count})不能大于场次限制数量({session_limit})")
    self.strict_validation_failed = True
```

### 2. `enhanced_validation_rules.md`

**修改位置**：第59-65行的场次限制验证部分

**更新内容**：
- 更新了验证规则描述
- 标记了已删除的规则
- 添加了新的验证规则说明

## 测试验证

创建了 `test_proctor_validation_rules.py` 测试脚本，验证结果：

### 测试案例1：符合新规则的数据
- ✅ 验证通过
- ✅ 监考员设置验证通过

### 测试案例2：违反新规则1
- ✅ 正确检测到错误：`必监考科目数量(2)+不监考科目数量(2)之和(4)不能超过考试所有科目总和(3)`

### 测试案例3：违反新规则2
- ✅ 正确检测到错误：`必监考科目数量(3)不能大于场次限制数量(2)`

## 规则逻辑对比

### 旧规则逻辑
```
规则1: 必监考科目数量 + 场次限制 ≤ 总科目数
规则2: 必监考科目数量 + 不监考科目数量 ≤ 总科目数
```

### 新规则逻辑
```
规则1: 必监考科目数量 + 不监考科目数量 ≤ 总科目数
规则2: 必监考科目数量 ≤ 场次限制数量
```

## 业务逻辑说明

### 新规则1的意义
- **目的**：确保监考员的科目偏好设置合理
- **逻辑**：一个监考员指定的必监考科目和不监考科目的总数不应超过所有考试科目的总数
- **示例**：如果总共有3个科目，监考员不能同时指定2个必监考科目和2个不监考科目

### 新规则2的意义
- **目的**：确保监考员的必监考科目数量与其工作能力匹配
- **逻辑**：监考员的必监考科目数量不能超过其场次限制
- **示例**：如果监考员场次限制是2，则不能指定3个必监考科目

## 影响范围

这次更新主要影响：

1. **向导页面验证**：
   - 第四步预览与验证功能
   - 监考员设置数据的验证逻辑

2. **错误提示**：
   - 更新了错误信息的格式和内容
   - 提供更清晰的验证失败原因

3. **数据完整性**：
   - 确保监考员设置的逻辑一致性
   - 提高数据质量和系统稳定性

## 总结

这次监考员设置验证规则更新成功实现了用户要求：

- ✅ 删除了两个旧的验证规则
- ✅ 添加了两个新的验证规则
- ✅ 保持了代码的清晰性和可维护性
- ✅ 通过了完整的测试验证
- ✅ 更新了相关文档

新的验证规则更加合理和实用，能够更好地确保监考员设置数据的逻辑一致性。
