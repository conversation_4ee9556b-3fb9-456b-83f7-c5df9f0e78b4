#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import pandas as pd
import uuid
import logging
from datetime import datetime
from typing import Dict, List, Callable, Optional, Set, Tuple, Any

# 配置日志
logger = logging.getLogger('jiankao_app.validator')

class ExcelValidator:
    """Excel文件验证器：用于验证上传的Excel文件是否符合系统要求"""

    def __init__(self, file_path: str, progress_callback: Optional[Callable[[int, str], None]] = None):
        """
        初始化验证器

        参数:
            file_path: Excel文件路径
            progress_callback: 进度回调函数，接收进度百分比和消息
        """
        self.file_path = file_path
        self.progress_callback = progress_callback
        self.workbook = None
        self.errors = []
        self.warnings = []
        self.validation_messages = {
            'errors': [],
            'warnings': [],
            'info': [],
            'examples': {}
        }
        self.is_valid = True
        self.error_details = []
        self.subjects_data = {}
        self.proctors_data = {}
        self.rooms_data = {}

    def close(self):
        """关闭并释放资源"""
        try:
            if hasattr(self, 'workbook') and self.workbook:
                self.workbook.close()
                self.workbook = None
        except Exception as e:
            logger.error(f"关闭Excel文件时发生错误: {str(e)}")

    def __del__(self):
        """析构函数，确保资源被释放"""
        self.close()

    def validate(self) -> bool:
        """
        执行所有验证步骤

        返回:
            bool: 验证是否通过
        """
        try:
            self.workbook = pd.ExcelFile(self.file_path)
            self._update_progress(10, "开始验证文件...")

            required_sheets = ['监考员设置', '考试科目设置', '考场设置']
            missing_sheets = [s for s in required_sheets if s not in self.workbook.sheet_names]

            if missing_sheets:
                self.errors.append(f"缺少必要的工作表: {', '.join(missing_sheets)}")

            self._update_progress(20, "检查工作表...")

            if '监考员设置' in self.workbook.sheet_names:
                self._validate_teacher_sheet()
                self._update_progress(40, "检查监考员设置...")

            if '考试科目设置' in self.workbook.sheet_names:
                self._validate_subject_sheet()
                self._update_progress(60, "检查考试科目设置...")

            if '考场设置' in self.workbook.sheet_names:
                self._validate_room_sheet()
                self._update_progress(80, "检查考场设置...")

            self._validate_total_supervision_capacity()
            self._update_progress(90, "验证监考员总场次限制...")

            is_valid = len(self.errors) == 0
            if not is_valid:
                self._generate_error_report()

            self._update_progress(100, "验证完成")
            logger.info(f"验证完成，错误数量：{len(self.errors)}，警告数量：{len(self.warnings)}")

            if not is_valid:
                for error in self.errors:
                    logger.error(f"验证错误: {error}")
            if self.warnings:
                for warning in self.warnings:
                    logger.warning(f"验证警告: {warning}")

            return is_valid

        except Exception as e:
            error_msg = f"验证过程发生错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            self.errors.append(error_msg)
            self.is_valid = False
            self._update_progress(100, "验证失败")
            return False

    def get_validation_messages(self) -> Dict[str, List[str]]:
        return {
            'errors': self.errors.copy(),
            'warnings': self.warnings.copy()
        }

    def _generate_error_report(self) -> Optional[str]:
        try:
            report_filename = f"error_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            report_path = os.path.join(os.path.dirname(self.file_path), report_filename)
            with pd.ExcelWriter(report_path) as writer:
                error_summary = pd.DataFrame({
                    '错误类型': ['验证错误'] * len(self.errors) + ['警告'] * len(self.warnings),
                    '描述': self.errors + self.warnings
                })
                error_summary.to_excel(writer, sheet_name='错误摘要', index=False)
                if self.error_details:
                    error_details = pd.DataFrame(self.error_details)
                    error_details.to_excel(writer, sheet_name='详细错误', index=False)
            logger.info(f"错误报告已生成: {report_path}")
            return report_path
        except Exception as e:
            logger.error(f"生成错误报告时发生错误: {str(e)}", exc_info=True)
            return None

    def add_warning(self, message: str) -> None:
        if message not in self.warnings:
            self.warnings.append(message)
            logger.warning(f"添加警告: {message}")

    def add_error_detail(self, sheet: str, row: int, column: str, value: str, error_type: str, message: str) -> None:
        detail = {
            '工作表': sheet,
            '行号': row,
            '列名': column,
            '错误值': value,
            '错误类型': error_type,
            '错误描述': message
        }
        self.error_details.append(detail)
        logger.error(f"添加错误详情: {detail}")

    def _update_progress(self, progress: int, message: str) -> None:
        logger.info(f"验证进度: {progress}% - {message}")
        if self.progress_callback:
            self.progress_callback(progress, message)

    def _validate_teacher_sheet(self):
        sheet = pd.read_excel(self.workbook, sheet_name='监考员设置')
        required_columns = ['序号', '监考老师', '任教科目', '场次限制']
        missing_columns = [col for col in required_columns if col not in sheet.columns]
        if missing_columns:
            self.errors.append(f"监考员设置表缺少必要的列: {', '.join(missing_columns)}")
        for idx, row in sheet.iterrows():
            if pd.isna(row.get('序号')):
                self.errors.append(f"监考员设置表第{idx+2}行序号不能为空")
            if pd.isna(row.get('监考老师')):
                self.errors.append(f"监考员设置表第{idx+2}行监考老师不能为空")
            if '场次限制' in sheet.columns:
                limit_value = row['场次限制']
                if pd.isna(limit_value):
                    self.errors.append(f"监考员设置表第{idx+2}行场次限制不能为空")
                else:
                    try:
                        if int(limit_value) <= 0:
                            self.errors.append(f"监考员设置表第{idx+2}行场次限制必须是大于0的整数")
                    except (ValueError, TypeError):
                        self.errors.append(f"监考员设置表第{idx+2}行场次限制必须为整数")

    def _validate_subject_sheet(self):
        try:
            sheet = pd.read_excel(self.workbook, sheet_name='考试科目设置')
        except Exception as e:
            self.errors.append(f"无法读取考试科目设置工作表: {str(e)}")
            return

        # 修改为新的验证规则：必须包含课程代码、课程名称、开始时间、结束时间，内容允许为空值
        required_columns = ['课程代码', '课程名称', '开始时间', '结束时间']
        missing_columns = [col for col in required_columns if col not in sheet.columns]
        if missing_columns:
            self.errors.append(f"考试科目设置表缺少必要的列: {', '.join(missing_columns)}")
            return

        for index, row in sheet.iterrows():
            row_num = index + 2

            # 检查时间格式（仅对非空值进行格式验证）
            start_time_str = str(row.get('开始时间', '')).strip()
            end_time_str = str(row.get('结束时间', '')).strip()

            # 开始时间格式检查（允许空值）
            if start_time_str and start_time_str.lower() != 'nan' and start_time_str != '':
                try:
                    # 尝试解析时间 - 支持 HH:MM 或 yyyy/mm/dd hh:mm 格式
                    if ':' in start_time_str and len(start_time_str.split()) == 1:
                        # HH:MM 格式
                        datetime.strptime(start_time_str, '%H:%M')
                    else:
                        # yyyy/mm/dd hh:mm 格式
                        datetime.strptime(start_time_str, '%Y/%m/%d %H:%M')
                except ValueError:
                    self.errors.append(f"考试科目设置表第{row_num}行开始时间格式错误，应为 HH:MM 或 yyyy/mm/dd hh:mm 格式")

            # 结束时间格式检查（允许空值）
            if end_time_str and end_time_str.lower() != 'nan' and end_time_str != '':
                try:
                    # 尝试解析时间 - 支持 HH:MM 或 yyyy/mm/dd hh:mm 格式
                    if ':' in end_time_str and len(end_time_str.split()) == 1:
                        # HH:MM 格式
                        datetime.strptime(end_time_str, '%H:%M')
                    else:
                        # yyyy/mm/dd hh:mm 格式
                        datetime.strptime(end_time_str, '%Y/%m/%d %H:%M')
                except ValueError:
                    self.errors.append(f"考试科目设置表第{row_num}行结束时间格式错误，应为 HH:MM 或 yyyy/mm/dd hh:mm 格式")

            # 注意：根据新规则，课程代码和课程名称的内容也允许为空值，不再强制要求非空

        self.subjects_data['subjects'] = sheet.to_dict('records')

    def _validate_room_sheet(self):
        sheet = pd.read_excel(self.workbook, sheet_name='考场设置')
        if sheet.empty or '考场' not in sheet.columns:
            self.errors.append("考场设置表为空或缺少考场列")
        for idx, row in sheet.iterrows():
            if pd.isna(row.get('考场')):
                self.errors.append(f"考场设置表第{idx+2}行考场编号为空")

    def _validate_total_supervision_capacity(self) -> bool:
        try:
            teacher_sheet = pd.read_excel(self.workbook, sheet_name='监考员设置')
            if '场次限制' not in teacher_sheet.columns:
                self.errors.append("监考员设置表缺少场次限制列")
                return False
            total_capacity = 0
            for limit_value in teacher_sheet['场次限制']:
                if pd.notna(limit_value):
                    try:
                        total_capacity += int(limit_value)
                    except (ValueError, TypeError):
                        pass
            room_sheet = pd.read_excel(self.workbook, sheet_name='考场设置')
            subject_columns = [col for col in room_sheet.columns if col != '考场']
            total_required = sum(room_sheet[subject].sum() for subject in subject_columns)
            if total_capacity < total_required:
                self.errors.append(f"监考员总场次限制不足，当前限制（{total_capacity}）小于所需总数（{total_required}）")
            return True
        except Exception as e:
            self.errors.append(f"验证监考员总场次限制时发生错误: {str(e)}")
            return False

class ExtendedExcelValidator(ExcelValidator):
    def __init__(self, file_path: str, progress_callback: Optional[Callable[[int, str], None]] = None):
        super().__init__(file_path, progress_callback)

    def validate(self) -> bool:
        self._update_progress(0, "开始验证...")
        base_valid = super().validate()
        self._update_progress(10, "执行基础验证...")
        if not base_valid:
            self.is_valid = False
            self._update_progress(100, "基础验证失败")
            return False
        try:
            self.validate_proctors()
            self._update_progress(50, "验证监考员设置...")
            self.validate_rooms()
            self._update_progress(75, "验证考场设置...")
            self.cross_validate()
            self._update_progress(90, "执行交叉验证...")
            self.is_valid = len(self.errors) == 0
            if not self.is_valid:
                self._generate_error_report()
            self._update_progress(100, "扩展验证完成")
            return self.is_valid
        except Exception as e:
            self.errors.append(f"扩展验证过程发生错误: {str(e)}")
            self.is_valid = False
            self._update_progress(100, "扩展验证失败")
            return False

    def validate_proctors(self):
        try:
            df = pd.read_excel(self.file_path, sheet_name='监考员设置')
            df.columns = df.columns.str.strip()
            required_cols = {'监考老师', '场次限制'}
            if not required_cols.issubset(df.columns):
                self.errors.append(f"监考员设置表缺少列: {', '.join(required_cols - set(df.columns))}")
                return
            self.proctors_data['proctors'] = df.to_dict('records')
        except Exception as e:
            self.errors.append(f"读取或验证监考员设置工作表时出错: {str(e)}")

    def validate_rooms(self):
        try:
            df = pd.read_excel(self.file_path, sheet_name='考场设置')
            df.columns = df.columns.str.strip()
            if '考场' not in df.columns:
                self.errors.append("考场设置表缺少考场列")
                return
            self.rooms_data['rooms'] = df.to_dict('records')
        except Exception as e:
            self.errors.append(f"读取或验证考场设置工作表时出错: {str(e)}")

    def cross_validate(self):
        if not self.subjects_data.get('subjects'):
            return
            
        subject_names = {s['考试科目'] for s in self.subjects_data.get('subjects', [])}
        room_subjects = set()
        rooms_df = pd.DataFrame(self.rooms_data.get('rooms', []))
        if not rooms_df.empty:
            room_subjects = set(rooms_df.columns) - {'考场'}
        undefined_subjects = room_subjects - subject_names
        if undefined_subjects:
            self.errors.append(f"考场设置表中包含未在考试科目设置表中定义的科目: {', '.join(undefined_subjects)}")
        proctors = self.proctors_data.get('proctors', [])
        for proctor in proctors:
            for key in ['必监考科目', '不监考科目']:
                if key in proctor and pd.notna(proctor[key]):
                    subjects = str(proctor[key]).split(',')
                    for s in subjects:
                        s_stripped = s.strip()
                        if s_stripped and s_stripped not in subject_names:
                            self.warnings.append(f"监考员{proctor.get('监考老师')}的{key}中包含未定义的科目: {s_stripped}")
