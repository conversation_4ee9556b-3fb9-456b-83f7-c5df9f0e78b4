# 表格居中显示修改说明

## 修改概述

根据用户要求，为向导页面第四步预览中的监考员预览数据表格添加居中显示样式，同时为保持一致性，也为考试科目和考场预览表格添加了相同的样式。

## 修改内容

### 修改文件
`templates/wizard/step4_review.html`

### 修改的表格

#### 1. 考试科目预览表格（第92-113行）
**修改前**：
```html
<table class="table table-sm table-bordered table-striped">
    <thead class="table-light">
        <tr>
            <th>序号</th>
            <th>课程代码</th>
            <th>课程名称</th>
            <th>开始时间</th>
            <th>结束时间</th>
        </tr>
    </thead>
    <tbody>
        {% for subject in wizard_data.get('subjects', []) %}
        <tr>
            <td>{{ loop.index }}</td>
            <td>{{ subject.subject_code }}</td>
            <td>{{ subject.subject_name }}</td>
            <td>{{ subject.start_time }}</td>
            <td>{{ subject.end_time }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**修改后**：
```html
<table class="table table-sm table-bordered table-striped text-center">
    <thead class="table-light">
        <tr>
            <th class="text-center">序号</th>
            <th class="text-center">课程代码</th>
            <th class="text-center">课程名称</th>
            <th class="text-center">开始时间</th>
            <th class="text-center">结束时间</th>
        </tr>
    </thead>
    <tbody>
        {% for subject in wizard_data.get('subjects', []) %}
        <tr>
            <td class="text-center">{{ loop.index }}</td>
            <td class="text-center">{{ subject.subject_code }}</td>
            <td class="text-center">{{ subject.subject_name }}</td>
            <td class="text-center">{{ subject.start_time }}</td>
            <td class="text-center">{{ subject.end_time }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

#### 2. 监考员预览表格（第121-148行）
**修改前**：
```html
<table class="table table-sm table-bordered table-striped">
    <thead class="table-light">
        <tr>
            <th>序号</th>
            <th>监考老师</th>
            <th>任教科目</th>
            <th>必监考科目</th>
            <th>不监考科目</th>
            <th>必监考考场</th>
            <th>不监考考场</th>
            <th>场次限制</th>
        </tr>
    </thead>
    <tbody>
        {% for proctor in wizard_data.get('proctors', []) %}
        <tr>
            <td>{{ loop.index }}</td>
            <td>{{ proctor.name }}</td>
            <td>{{ proctor.teaching_subject }}</td>
            <td>{{ proctor.required_subjects | join(', ') }}</td>
            <td>{{ proctor.unavailable_subjects | join(', ') }}</td>
            <td>{{ proctor.required_rooms | join(', ') if proctor.required_rooms else '' }}</td>
            <td>{{ proctor.unavailable_rooms | join(', ') if proctor.unavailable_rooms else '' }}</td>
            <td>{{ proctor.session_limit }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**修改后**：
```html
<table class="table table-sm table-bordered table-striped text-center">
    <thead class="table-light">
        <tr>
            <th class="text-center">序号</th>
            <th class="text-center">监考老师</th>
            <th class="text-center">任教科目</th>
            <th class="text-center">必监考科目</th>
            <th class="text-center">不监考科目</th>
            <th class="text-center">必监考考场</th>
            <th class="text-center">不监考考场</th>
            <th class="text-center">场次限制</th>
        </tr>
    </thead>
    <tbody>
        {% for proctor in wizard_data.get('proctors', []) %}
        <tr>
            <td class="text-center">{{ loop.index }}</td>
            <td class="text-center">{{ proctor.name }}</td>
            <td class="text-center">{{ proctor.teaching_subject }}</td>
            <td class="text-center">{{ proctor.required_subjects | join(', ') }}</td>
            <td class="text-center">{{ proctor.unavailable_subjects | join(', ') }}</td>
            <td class="text-center">{{ proctor.required_rooms | join(', ') if proctor.required_rooms else '' }}</td>
            <td class="text-center">{{ proctor.unavailable_rooms | join(', ') if proctor.unavailable_rooms else '' }}</td>
            <td class="text-center">{{ proctor.session_limit }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

#### 3. 考场预览表格（第156-175行）
**修改前**：
```html
<table class="table table-sm table-bordered table-striped">
    <thead class="table-light">
        <tr>
            <th>考场名称</th>
            {% for subject in wizard_data.get('subjects', []) %}
            <th>{{ subject.subject_name }} (人数)</th>
            {% endfor %}
        </tr>
    </thead>
    <tbody>
        {% for room in wizard_data.get('rooms', []) %}
        <tr>
            <td>{{ room.name }}</td>
            {% for subject in wizard_data.get('subjects', []) %}
            <td>{{ room.demands.get(subject.subject_name, 'N/A') }}</td>
            {% endfor %}
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**修改后**：
```html
<table class="table table-sm table-bordered table-striped text-center">
    <thead class="table-light">
        <tr>
            <th class="text-center">考场名称</th>
            {% for subject in wizard_data.get('subjects', []) %}
            <th class="text-center">{{ subject.subject_name }} (人数)</th>
            {% endfor %}
        </tr>
    </thead>
    <tbody>
        {% for room in wizard_data.get('rooms', []) %}
        <tr>
            <td class="text-center">{{ room.name }}</td>
            {% for subject in wizard_data.get('subjects', []) %}
            <td class="text-center">{{ room.demands.get(subject.subject_name, 'N/A') }}</td>
            {% endfor %}
        </tr>
        {% endfor %}
    </tbody>
</table>
```

## 技术实现

### Bootstrap CSS 类
使用了 Bootstrap 的 `text-center` 类来实现文本居中对齐：

1. **表格级别**：在 `<table>` 标签上添加 `text-center` 类
2. **表头级别**：在每个 `<th>` 标签上添加 `text-center` 类
3. **单元格级别**：在每个 `<td>` 标签上添加 `text-center` 类

### 样式层次
```css
.text-center {
    text-align: center !important;
}
```

这确保了：
- 表头文字居中显示
- 表格数据居中显示
- 覆盖任何默认的左对齐样式

## 视觉效果改进

### 修改前
- 表格内容默认左对齐
- 数字和文本混合时视觉不够整齐
- 表格看起来不够规整

### 修改后
- 所有表格内容居中对齐
- 数字和文本都整齐排列
- 表格视觉效果更加专业和美观
- 提高了数据的可读性

## 用户体验提升

### 1. 视觉一致性
- 所有预览表格使用统一的居中对齐样式
- 提供了更加专业的视觉体验

### 2. 数据可读性
- 居中对齐使得数据更容易扫描和比较
- 特别是对于数字数据（如场次限制、人数需求）更加清晰

### 3. 界面美观性
- 居中对齐的表格看起来更加整洁
- 符合现代Web应用的设计标准

## 兼容性说明

### Bootstrap 兼容性
- 使用标准的Bootstrap CSS类，确保跨浏览器兼容性
- 不依赖自定义CSS，降低维护成本

### 响应式设计
- `text-center` 类在所有屏幕尺寸下都能正常工作
- 配合 `table-responsive` 类确保移动端体验

## 影响范围

### 直接影响
- **向导第四步预览页面**：所有表格数据现在居中显示
- **用户界面体验**：提供更专业、更整洁的数据展示

### 无副作用
- ✅ 不影响数据处理逻辑
- ✅ 不影响其他页面
- ✅ 保持响应式设计
- ✅ 不破坏现有功能

## 总结

这次修改成功实现了表格数据的居中显示：

- ✅ **监考员预览表格**：所有单元格内容居中显示
- ✅ **考试科目预览表格**：保持视觉一致性
- ✅ **考场预览表格**：统一的显示风格
- ✅ **用户体验**：更专业、更美观的界面
- ✅ **技术实现**：使用标准Bootstrap类，简洁可靠

修改后的页面将为用户提供更好的视觉体验，所有表格数据都以整齐、专业的方式居中显示。
