# 监考员姓名验证修复说明

## 问题描述

用户报告在向导第三步监考员页面中，通过点击"添加监考员"按钮，在页面中输入监考员姓名信息后，按下一步按钮时，页面弹出窗口提示："监考老师"的姓名不能为空。

## 问题分析

### 问题根源

在 `templates/wizard/step3_proctors.html` 文件的表单提交验证逻辑中存在以下问题：

1. **数据同步问题**：当没有筛选或排序时，`syncCurrentEditsToOriginalData()` 函数不会执行（第1537行的条件判断），导致用户在DOM中输入的数据没有被同步到 `proctors` 数组中。

2. **验证逻辑错误**：表单提交时验证的是旧的 `proctors` 数组，而不是用户刚刚在表单中输入的最新数据。

3. **验证逻辑不完整**：原来的验证逻辑只检查第一个监考员（`proctors[0].name`），而且逻辑有错误。

### 原有问题代码

```javascript
$('#wizard-form').submit(function(e) {
    // 先同步当前编辑到原始数据
    syncCurrentEditsToOriginalData();  // 当没有筛选时不会执行
    let isValid = true;

    if (proctors.length > 0) {
        const name = proctors[0].name;  // 只检查第一个监考员
        if (!name) {
             if (proctors.length === 1) {
                proctors = [];
            } else {
            isValid = false;
            }
        }
    }
    
    if (proctors.length > 0) {
        isValid = !proctors.some(p => !p.name);  // 检查的是旧数据
    }

    if (!isValid) {
        e.preventDefault();
        alert('"监考老师"的姓名不能为空。');
        return;
    }
    // ...
});
```

### `syncCurrentEditsToOriginalData` 函数的问题

```javascript
function syncCurrentEditsToOriginalData() {
    // 只在有筛选的情况下才需要同步，因为无筛选时显示的就是完整数据
    if (Object.keys(currentFilters).length > 0 || currentSort.field) {
        // 同步逻辑...
    }
    // 当没有筛选时，这个函数什么都不做！
}
```

## 修复方案

### 修复后的代码

```javascript
$('#wizard-form').submit(function(e) {
    // 总是从DOM收集最新的数据，不管是否有筛选
    const currentData = collectDataFromDOM();
    
    // 如果有筛选或排序，需要将编辑同步到原始数据
    if (Object.keys(currentFilters).length > 0 || currentSort.field) {
        syncCurrentEditsToOriginalData();
    } else {
        // 如果没有筛选，直接使用DOM中的数据
        proctors = currentData;
    }
    
    let isValid = true;

    // 检查是否有监考员
    if (proctors.length === 0) {
        e.preventDefault();
        alert('请至少添加一名监考员。');
        return;
    }
    
    // 检查所有监考员的姓名是否为空
    const emptyNameProctors = proctors.filter(p => !p.name || !p.name.trim());
    if (emptyNameProctors.length > 0) {
        e.preventDefault();
        alert('"监考老师"的姓名不能为空。');
        return;
    }

    $('#proctors_data').val(JSON.stringify(proctors));
});
```

### 修复要点

1. **总是收集最新数据**：
   - 使用 `collectDataFromDOM()` 函数总是从DOM中收集最新的用户输入数据
   - 不依赖于是否有筛选条件

2. **正确的数据同步**：
   - 当有筛选或排序时，使用现有的同步逻辑
   - 当没有筛选时，直接使用DOM中收集的数据

3. **完整的验证逻辑**：
   - 检查所有监考员，不只是第一个
   - 使用 `filter()` 方法找出所有姓名为空的监考员
   - 同时检查 `null`、`undefined` 和空字符串

4. **更清晰的错误处理**：
   - 分别处理"没有监考员"和"姓名为空"两种情况
   - 提供更准确的错误提示

## 技术细节

### `collectDataFromDOM` 函数

这个函数负责从DOM中收集用户输入的数据：

```javascript
function collectDataFromDOM() {
    const collectedProctors = [];
    $('#proctors-list tr').each(function() {
        const name = $(this).find('input[name="proctor_name"]').val().trim();
        // 收集其他字段...
        collectedProctors.push({
            name: name,
            teaching_subject: $(this).find('input[name="teaching_subject"]').val().trim(),
            // 其他字段...
        });
    });
    return collectedProctors;
}
```

### 数据流程

1. **用户操作**：用户点击"添加监考员"，在表单中输入姓名
2. **DOM状态**：数据存在于DOM的input元素中
3. **提交验证**：表单提交时调用 `collectDataFromDOM()` 获取最新数据
4. **数据同步**：根据是否有筛选，选择合适的同步策略
5. **验证检查**：对最新的数据进行验证
6. **提交数据**：将验证通过的数据序列化并提交

## 测试场景

### 场景1：正常添加监考员
1. 点击"添加监考员"按钮
2. 输入监考员姓名"张老师"
3. 点击"下一步"
4. **期望结果**：成功提交，进入下一步

### 场景2：添加空姓名监考员
1. 点击"添加监考员"按钮
2. 不输入姓名或输入空字符串
3. 点击"下一步"
4. **期望结果**：显示错误提示"监考老师的姓名不能为空"

### 场景3：多个监考员混合情况
1. 添加多个监考员，其中一些有姓名，一些没有
2. 点击"下一步"
3. **期望结果**：显示错误提示，要求填写所有监考员姓名

### 场景4：有筛选条件时的验证
1. 添加监考员并设置筛选条件
2. 在筛选状态下编辑监考员信息
3. 点击"下一步"
4. **期望结果**：正确验证筛选后的数据

## 影响范围

### 直接影响
- **向导第三步监考员页面**：修复了姓名验证问题
- **用户体验**：用户输入姓名后能正常进入下一步

### 无副作用
- ✅ 不影响其他验证逻辑
- ✅ 不影响筛选和排序功能
- ✅ 保持向后兼容性
- ✅ 不影响Excel导入功能

## 总结

这次修复解决了监考员姓名验证的核心问题：

- ✅ **数据同步**：确保验证时使用的是用户最新输入的数据
- ✅ **验证逻辑**：检查所有监考员的姓名，不遗漏任何一个
- ✅ **错误处理**：提供准确的错误提示信息
- ✅ **兼容性**：保持与筛选、排序功能的兼容性

修复后，用户在添加监考员并输入姓名后，能够正常通过验证并进入下一步，解决了"姓名不能为空"的误报问题。
