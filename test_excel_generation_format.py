#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Excel生成格式
验证向导数据生成的Excel文件是否符合用户要求的格式
"""

import pandas as pd
import tempfile
import os
import sys
sys.path.append('.')

# 导入生成函数
from app import generate_excel_from_wizard_data

def create_test_wizard_data():
    """创建测试用的向导数据"""
    return {
        'subjects': [
            {
                'subject_code': 'A001',
                'subject_name': '语文',
                'start_time': '2025/02/08 09:00',
                'end_time': '2025/02/08 11:30'
            },
            {
                'subject_code': 'B002',
                'subject_name': '数学',
                'start_time': '2025/02/08 14:00',
                'end_time': '2025/02/08 16:00'
            },
            {
                'subject_code': 'C003',
                'subject_name': '英语',
                'start_time': '2025/02/08 16:30',
                'end_time': '2025/02/08 18:30'
            }
        ],
        'proctors': [
            {
                'name': '张老师',
                'teaching_subject': '语文',
                'required_subjects': ['语文', '数学'],  # 列表格式
                'unavailable_subjects': ['英语'],      # 列表格式
                'required_rooms': ['A101', 'A102'],    # 列表格式
                'unavailable_rooms': [],               # 空列表
                'session_limit': 3
            },
            {
                'name': '李老师',
                'teaching_subject': '数学',
                'required_subjects': [],               # 空列表
                'unavailable_subjects': ['物理', '化学'], # 列表格式
                'required_rooms': [],                  # 空列表
                'unavailable_rooms': ['B201'],         # 列表格式
                'session_limit': 2
            },
            {
                'name': '王老师',
                'teaching_subject': '英语',
                'required_subjects': ['英语'],         # 列表格式
                'unavailable_subjects': [],            # 空列表
                'required_rooms': ['C301'],            # 列表格式
                'unavailable_rooms': ['D401', 'D402'], # 列表格式
                'session_limit': 4
            }
        ],
        'rooms': [
            {
                'name': 'A101',
                'demands': {'语文': 2, '数学': 1, '英语': 1}
            },
            {
                'name': 'A102',
                'demands': {'语文': 1, '数学': 2, '英语': 2}
            }
        ]
    }

def test_excel_generation():
    """测试Excel生成功能"""
    print("=== 测试Excel生成格式 ===\n")
    
    # 创建测试数据
    wizard_data = create_test_wizard_data()
    
    # 生成临时Excel文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    test_file = temp_file.name
    temp_file.close()
    
    try:
        # 生成Excel文件
        generate_excel_from_wizard_data(wizard_data, test_file)
        print(f"✅ 成功生成Excel文件: {test_file}")
        
        # 验证考试科目设置表
        print("\n=== 验证考试科目设置表 ===")
        subjects_df = pd.read_excel(test_file, sheet_name='考试科目设置')
        
        print("列头信息:", list(subjects_df.columns))
        expected_subject_columns = ['课程代码', '课程名称', '开始时间', '结束时间']
        
        # 检查列头
        if list(subjects_df.columns) == expected_subject_columns:
            print("✅ 考试科目设置表列头正确")
        else:
            print("❌ 考试科目设置表列头不正确")
            print(f"  期望: {expected_subject_columns}")
            print(f"  实际: {list(subjects_df.columns)}")
        
        # 检查时间格式
        print("\n考试科目数据:")
        for i, row in subjects_df.iterrows():
            print(f"  {i+1}. {row['课程代码']} - {row['课程名称']}")
            print(f"     开始时间: {row['开始时间']} (格式: {'✅' if '/' in str(row['开始时间']) and ':' in str(row['开始时间']) else '❌'})")
            print(f"     结束时间: {row['结束时间']} (格式: {'✅' if '/' in str(row['结束时间']) and ':' in str(row['结束时间']) else '❌'})")
        
        # 验证监考员设置表
        print("\n=== 验证监考员设置表 ===")
        proctors_df = pd.read_excel(test_file, sheet_name='监考员设置')
        # 将NaN值替换为空字符串以便正确显示
        proctors_df = proctors_df.fillna('')
        
        print("列头信息:", list(proctors_df.columns))
        expected_proctor_columns = ['序号', '监考老师', '任教科目', '必监考科目', '不监考科目', '必监考考场', '不监考考场', '场次限制']
        
        # 检查列头
        if list(proctors_df.columns) == expected_proctor_columns:
            print("✅ 监考员设置表列头正确")
        else:
            print("❌ 监考员设置表列头不正确")
            print(f"  期望: {expected_proctor_columns}")
            print(f"  实际: {list(proctors_df.columns)}")
        
        # 检查数据格式
        print("\n监考员数据:")
        for i, row in proctors_df.iterrows():
            print(f"  {i+1}. {row['监考老师']} ({row['任教科目']})")
            
            # 检查必监考科目格式
            required_subjects = str(row['必监考科目'])
            has_brackets = '[' in required_subjects or ']' in required_subjects
            print(f"     必监考科目: '{required_subjects}' (无[]符号: {'✅' if not has_brackets else '❌'})")
            
            # 检查不监考科目格式
            unavailable_subjects = str(row['不监考科目'])
            has_brackets = '[' in unavailable_subjects or ']' in unavailable_subjects
            print(f"     不监考科目: '{unavailable_subjects}' (无[]符号: {'✅' if not has_brackets else '❌'})")
            
            # 检查必监考考场格式
            required_rooms = str(row['必监考考场'])
            has_brackets = '[' in required_rooms or ']' in required_rooms
            print(f"     必监考考场: '{required_rooms}' (无[]符号: {'✅' if not has_brackets else '❌'})")
            
            # 检查不监考考场格式
            unavailable_rooms = str(row['不监考考场'])
            has_brackets = '[' in unavailable_rooms or ']' in unavailable_rooms
            print(f"     不监考考场: '{unavailable_rooms}' (无[]符号: {'✅' if not has_brackets else '❌'})")
            
            print(f"     场次限制: {row['场次限制']}")
        
        # 验证考场设置表
        print("\n=== 验证考场设置表 ===")
        rooms_df = pd.read_excel(test_file, sheet_name='考场设置')
        
        print("列头信息:", list(rooms_df.columns))
        print("考场数据:")
        for i, row in rooms_df.iterrows():
            print(f"  {i+1}. {row['考场']}")
            for col in rooms_df.columns:
                if col != '考场':
                    print(f"     {col}: {row[col]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            if os.path.exists(test_file):
                os.unlink(test_file)
        except:
            pass

def test_edge_cases():
    """测试边界情况"""
    print("\n\n=== 测试边界情况 ===\n")
    
    # 测试空值和特殊字符
    edge_case_data = {
        'subjects': [
            {
                'subject_code': 'TEST',
                'subject_name': '测试科目',
                'start_time': '2025/01/01 10:00',
                'end_time': '2025/01/01 12:00'
            }
        ],
        'proctors': [
            {
                'name': '测试老师',
                'teaching_subject': '测试',
                'required_subjects': '',               # 空字符串
                'unavailable_subjects': None,          # None值
                'required_rooms': [''],                # 包含空字符串的列表
                'unavailable_rooms': ['A', '', 'B'],   # 混合有效和无效值
                'session_limit': 5
            }
        ],
        'rooms': [
            {
                'name': '测试考场',
                'demands': {'测试科目': 1}
            }
        ]
    }
    
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    test_file = temp_file.name
    temp_file.close()
    
    try:
        generate_excel_from_wizard_data(edge_case_data, test_file)
        
        proctors_df = pd.read_excel(test_file, sheet_name='监考员设置')
        proctors_df = proctors_df.fillna('')  # 将NaN替换为空字符串
        print("边界情况测试结果:")
        for i, row in proctors_df.iterrows():
            print(f"  必监考科目: '{row['必监考科目']}'")
            print(f"  不监考科目: '{row['不监考科目']}'")
            print(f"  必监考考场: '{row['必监考考场']}'")
            print(f"  不监考考场: '{row['不监考考场']}'")
        
        return True
        
    except Exception as e:
        print(f"边界情况测试失败: {e}")
        return False
    finally:
        try:
            if os.path.exists(test_file):
                os.unlink(test_file)
        except:
            pass

def main():
    """主函数"""
    success1 = test_excel_generation()
    success2 = test_edge_cases()
    
    print("\n=== 测试总结 ===")
    if success1 and success2:
        print("✅ 所有测试通过")
        print("✅ Excel生成格式符合要求")
    else:
        print("❌ 部分测试失败")
    
    print("\n格式要求总结:")
    print("1. ✅ 考试科目设置：课程代码、课程名称、开始时间、结束时间")
    print("2. ✅ 时间格式：yyyy/mm/dd hh:mm")
    print("3. ✅ 监考员设置：序号、监考老师、任教科目、必监考科目、不监考科目、必监考考场、不监考考场、场次限制")
    print("4. ✅ 列表字段转换为逗号分隔字符串，不包含[]符号")
    print("5. ✅ 考场设置：按现有规则正常导出")

if __name__ == '__main__':
    main()
