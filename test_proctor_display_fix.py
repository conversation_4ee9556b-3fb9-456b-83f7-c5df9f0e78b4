#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试监考员预览数据显示修复
验证必监考考场、不监考考场空值不再显示[]
"""

from jinja2 import Template

def test_template_rendering():
    """测试模板渲染逻辑"""
    print("=== 测试监考员预览数据显示修复 ===\n")
    
    # 模拟监考员数据
    test_data = [
        {
            'name': '张老师',
            'teaching_subject': '语文',
            'required_subjects': ['语文'],
            'unavailable_subjects': [],
            'required_rooms': [],  # 空列表
            'unavailable_rooms': [],  # 空列表
            'session_limit': 3
        },
        {
            'name': '李老师',
            'teaching_subject': '数学',
            'required_subjects': ['数学', '英语'],
            'unavailable_subjects': ['物理'],
            'required_rooms': ['A101', 'A102'],  # 有值的列表
            'unavailable_rooms': ['B201'],  # 有值的列表
            'session_limit': 2
        },
        {
            'name': '王老师',
            'teaching_subject': '英语',
            'required_subjects': [],
            'unavailable_subjects': ['化学', '生物'],
            'required_rooms': ['C301'],  # 有值的列表
            'unavailable_rooms': [],  # 空列表
            'session_limit': 4
        }
    ]
    
    # 修复前的模板（会显示[]）
    old_template = Template("""
<table>
    <tr><th>监考老师</th><th>必监考科目</th><th>不监考科目</th><th>必监考考场</th><th>不监考考场</th></tr>
    {% for proctor in proctors %}
    <tr>
        <td>{{ proctor.name }}</td>
        <td>{{ proctor.required_subjects | join(', ') }}</td>
        <td>{{ proctor.unavailable_subjects | join(', ') }}</td>
        <td>{{ proctor.required_rooms }}</td>
        <td>{{ proctor.unavailable_rooms }}</td>
    </tr>
    {% endfor %}
</table>
    """)
    
    # 修复后的模板（空值不显示[]）
    new_template = Template("""
<table>
    <tr><th>监考老师</th><th>必监考科目</th><th>不监考科目</th><th>必监考考场</th><th>不监考考场</th></tr>
    {% for proctor in proctors %}
    <tr>
        <td>{{ proctor.name }}</td>
        <td>{{ proctor.required_subjects | join(', ') }}</td>
        <td>{{ proctor.unavailable_subjects | join(', ') }}</td>
        <td>{{ proctor.required_rooms | join(', ') if proctor.required_rooms else '' }}</td>
        <td>{{ proctor.unavailable_rooms | join(', ') if proctor.unavailable_rooms else '' }}</td>
    </tr>
    {% endfor %}
</table>
    """)
    
    print("修复前的渲染结果:")
    print("=" * 50)
    old_result = old_template.render(proctors=test_data)
    print(old_result)
    
    print("\n修复后的渲染结果:")
    print("=" * 50)
    new_result = new_template.render(proctors=test_data)
    print(new_result)
    
    # 检查修复效果
    print("\n修复效果分析:")
    print("=" * 50)
    
    # 检查是否还有[]出现
    old_has_brackets = '[]' in old_result
    new_has_brackets = '[]' in new_result
    
    print(f"修复前包含[]符号: {'是' if old_has_brackets else '否'}")
    print(f"修复后包含[]符号: {'是' if new_has_brackets else '否'}")
    
    if old_has_brackets and not new_has_brackets:
        print("✅ 修复成功：空值不再显示[]")
    elif not old_has_brackets and not new_has_brackets:
        print("ℹ️  原本就没有[]显示问题")
    else:
        print("❌ 修复失败：仍然显示[]")
    
    # 详细分析每个监考员的显示
    print("\n详细分析:")
    print("=" * 50)
    
    for i, proctor in enumerate(test_data, 1):
        print(f"\n监考员{i}: {proctor['name']}")
        print(f"  必监考考场: {proctor['required_rooms']} -> 显示: '{', '.join(proctor['required_rooms']) if proctor['required_rooms'] else ''}'")
        print(f"  不监考考场: {proctor['unavailable_rooms']} -> 显示: '{', '.join(proctor['unavailable_rooms']) if proctor['unavailable_rooms'] else ''}")
        
        # 检查是否为空
        required_empty = not proctor['required_rooms']
        unavailable_empty = not proctor['unavailable_rooms']
        
        if required_empty:
            print(f"    ✅ 必监考考场为空，应显示空字符串")
        else:
            print(f"    ℹ️  必监考考场有值，应显示: {', '.join(proctor['required_rooms'])}")
            
        if unavailable_empty:
            print(f"    ✅ 不监考考场为空，应显示空字符串")
        else:
            print(f"    ℹ️  不监考考场有值，应显示: {', '.join(proctor['unavailable_rooms'])}")

def test_edge_cases():
    """测试边界情况"""
    print("\n\n=== 测试边界情况 ===\n")
    
    edge_cases = [
        {
            'name': '测试老师1',
            'required_rooms': None,  # None值
            'unavailable_rooms': None
        },
        {
            'name': '测试老师2',
            'required_rooms': '',  # 空字符串
            'unavailable_rooms': ''
        },
        {
            'name': '测试老师3',
            'required_rooms': [''],  # 包含空字符串的列表
            'unavailable_rooms': ['', '']
        }
    ]
    
    template = Template("""
{% for proctor in proctors %}
{{ proctor.name }}:
  必监考考场: "{{ proctor.required_rooms | join(', ') if proctor.required_rooms else '' }}"
  不监考考场: "{{ proctor.unavailable_rooms | join(', ') if proctor.unavailable_rooms else '' }}"
{% endfor %}
    """)
    
    result = template.render(proctors=edge_cases)
    print("边界情况渲染结果:")
    print(result)
    
    # 检查是否有异常
    if 'None' in result:
        print("⚠️  警告：结果中包含None字符串")
    else:
        print("✅ 边界情况处理正常")

def main():
    """主函数"""
    test_template_rendering()
    test_edge_cases()
    
    print("\n=== 修复总结 ===")
    print("✅ 修改了模板文件 templates/wizard/step4_review.html")
    print("✅ 必监考考场和不监考考场现在使用 join 过滤器")
    print("✅ 空列表不再显示[]，而是显示空字符串")
    print("✅ 有值的列表正常显示，用逗号分隔")

if __name__ == '__main__':
    main()
