#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试考试时间分布检查功能
验证修复后的时间分布检查是否正确考虑日期
"""

import pandas as pd
import tempfile
import os
from extended_validator import ExtendedExcelValidator

def create_test_excel_no_overlap():
    """创建没有时间重叠的测试Excel文件（用户提供的实际数据）"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 用户提供的实际数据
    subjects_data = {
        '课程代码': ['A', 'B', 'C', 'D', 'E'],
        '课程名称': ['语文', '数学', '英语', '物理', '化学'],
        '考试科目': ['语文', '数学', '英语', '物理', '化学'],
        '考试日期': ['2025/02/08', '2025/02/08', '2025/02/08', '2025/02/09', '2025/02/10'],
        '开始时间': [
            '2025/02/08 09:00',  # 语文
            '2025/02/08 14:00',  # 数学
            '2025/02/08 16:30',  # 英语
            '2025/02/09 16:30',  # 物理（不同日期）
            '2025/02/10 16:30'   # 化学（不同日期）
        ],
        '结束时间': [
            '2025/02/08 11:30',  # 语文
            '2025/02/08 16:00',  # 数学
            '2025/02/08 18:30',  # 英语
            '2025/02/09 18:30',  # 物理（不同日期）
            '2025/02/10 18:30'   # 化学（不同日期）
        ]
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101', 'A102'],
        '语文': [1, 1],
        '数学': [1, 1],
        '英语': [1, 1],
        '物理': [1, 1],
        '化学': [1, 1]
    }
    
    # 监考员设置表
    proctors_data = {
        '序号': [1, 2, 3, 4, 5],
        '监考老师': ['张老师', '李老师', '王老师', '赵老师', '刘老师'],
        '任教科目': ['语文', '数学', '英语', '物理', '化学'],
        '必监考科目': ['', '', '', '', ''],
        '不监考科目': ['', '', '', '', ''],
        '必监考考场': ['', '', '', '', ''],
        '不监考考场': ['', '', '', '', ''],
        '场次限制': [3, 3, 3, 3, 3]
    }
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def create_test_excel_with_overlap():
    """创建有时间重叠的测试Excel文件"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 同一天有重叠时间
    subjects_data = {
        '课程代码': ['A', 'B', 'C'],
        '课程名称': ['语文', '数学', '英语'],
        '考试科目': ['语文', '数学', '英语'],
        '考试日期': ['2025/02/08', '2025/02/08', '2025/02/08'],
        '开始时间': [
            '2025/02/08 09:00',  # 语文
            '2025/02/08 10:00',  # 数学（与语文重叠）
            '2025/02/08 10:30'   # 英语（与数学重叠）
        ],
        '结束时间': [
            '2025/02/08 11:30',  # 语文
            '2025/02/08 12:00',  # 数学
            '2025/02/08 12:30'   # 英语
        ]
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101'],
        '语文': [1],
        '数学': [1],
        '英语': [1]
    }
    
    # 监考员设置表
    proctors_data = {
        '序号': [1, 2, 3],
        '监考老师': ['张老师', '李老师', '王老师'],
        '任教科目': ['语文', '数学', '英语'],
        '必监考科目': ['', '', ''],
        '不监考科目': ['', '', ''],
        '必监考考场': ['', '', ''],
        '不监考考场': ['', '', ''],
        '场次限制': [3, 3, 3]
    }
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def test_exam_time_distribution():
    """测试考试时间分布检查功能"""
    print("=== 测试考试时间分布检查功能 ===\n")
    
    # 测试案例1：用户提供的实际数据（没有重叠）
    print("测试案例1：用户提供的实际数据（不同日期，没有重叠）")
    no_overlap_file = create_test_excel_no_overlap()
    
    try:
        # 显示测试数据
        df = pd.read_excel(no_overlap_file, sheet_name='考试科目设置')
        print("考试时间安排:")
        for i, row in df.iterrows():
            print(f"  {i+1}. {row['课程名称']}: {row['开始时间']} - {row['结束时间']}")
        
        validator = ExtendedExcelValidator(no_overlap_file)
        result = validator.validate()
        
        print(f"\n验证结果: {'通过' if result else '失败'}")
        
        # 检查时间分布相关的警告
        time_warnings = [w for w in validator.warnings if '考试时间分布' in w or '同时进行' in w]
        if time_warnings:
            print("❌ 错误：检测到不应该存在的时间分布警告:")
            for warning in time_warnings:
                print(f"  警告: {warning}")
        else:
            print("✅ 正确：没有时间分布警告（因为没有重叠）")
        
    except Exception as e:
        print(f"测试案例1出错: {e}")
    finally:
        try:
            if os.path.exists(no_overlap_file):
                os.unlink(no_overlap_file)
        except:
            pass
    
    print("\n" + "="*50 + "\n")
    
    # 测试案例2：有时间重叠的数据
    print("测试案例2：同一天有时间重叠的数据")
    overlap_file = create_test_excel_with_overlap()
    
    try:
        # 显示测试数据
        df = pd.read_excel(overlap_file, sheet_name='考试科目设置')
        print("考试时间安排:")
        for i, row in df.iterrows():
            print(f"  {i+1}. {row['课程名称']}: {row['开始时间']} - {row['结束时间']}")
        
        validator = ExtendedExcelValidator(overlap_file)
        result = validator.validate()
        
        print(f"\n验证结果: {'通过' if result else '失败'}")
        
        # 检查时间分布相关的警告
        time_warnings = [w for w in validator.warnings if '考试时间分布' in w or '同时进行' in w]
        if time_warnings:
            print("✅ 正确：检测到时间分布警告（因为有重叠）:")
            for warning in time_warnings:
                print(f"  警告: {warning}")
        else:
            print("❌ 错误：未检测到应该存在的时间分布警告")
        
    except Exception as e:
        print(f"测试案例2出错: {e}")
    finally:
        try:
            if os.path.exists(overlap_file):
                os.unlink(overlap_file)
        except:
            pass

def main():
    """主函数"""
    test_exam_time_distribution()
    
    print("\n=== 考试时间分布检查修复总结 ===")
    print("✅ 修复了日期处理问题")
    print("✅ 不同日期的相同时间段不再被误认为重叠")
    print("✅ 只有真正重叠的时间段才会触发警告")
    print("✅ 保留了完整的日期时间信息进行比较")

if __name__ == '__main__':
    main()
