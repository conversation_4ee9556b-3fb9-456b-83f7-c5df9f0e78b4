# 考试科目设置验证规则修改说明

## 修改概述

根据用户要求，修改了向导数据验证规则中的考试科目设置验证规则。

## 新的验证规则

### 1. 考试科目设置验证规则

**要求：**
- 监考员信息中必须包含以下表头的信息：
  - 课程代码
  - 课程名称  
  - 开始时间
  - 结束时间
- 内容允许为空值
- 表头必须包含这四列，但可以有其他列

## 修改的文件

### 1. `extended_validator.py`

#### 修改的方法：

**`_validate_subject_settings()`** (第398-436行)
- 更新必需列头检查：只要求 `['课程代码', '课程名称', '开始时间', '结束时间']`
- 移除了对 `'考试科目'` 和 `'考试日期'` 的强制要求
- 修改时间格式验证：只对非空值进行验证
- 更新注释说明新的验证规则

**`_check_time_overlap()`** (第446-484行)  
- 增强空值处理：只对有效的非空时间进行重叠检查
- 改进条件判断：`start_time_str != '' and end_time_str != ''`

**`_validate_subject_sheet()`** (第328-414行)
- 更新为兼容新验证规则的旧版本方法
- 支持多种时间格式：`HH:MM` 和 `yyyy/mm/dd hh:mm`
- 只对非空值进行格式验证和重叠检查

### 2. `excel_validator.py`

#### 修改的方法：

**`_validate_subject_sheet()`** (第182-231行)
- 更新必需列头：从 `['课程代码', '课程名称', '考试科目', '考试日期', '开始时间', '结束时间']` 改为 `['课程代码', '课程名称', '开始时间', '结束时间']`
- 移除对课程代码和课程名称非空的强制要求
- 支持多种时间格式验证
- 只对非空时间值进行格式检查

## 验证规则对比

### 修改前
```
必需列头: ['课程代码', '课程名称', '考试科目', '考试日期', '开始时间', '结束时间']
内容要求: 所有字段都不能为空
时间格式: 严格的格式要求
```

### 修改后  
```
必需列头: ['课程代码', '课程名称', '开始时间', '结束时间']
内容要求: 允许为空值
时间格式: 仅对非空值进行格式验证
其他列: 允许存在其他列
```

## 测试验证

创建了 `test_new_validation_rules.py` 测试脚本，验证结果：

### 测试1：符合新规则的文件
- ✅ 包含必需列头：课程代码、课程名称、开始时间、结束时间
- ✅ 内容允许为空值
- ✅ 可以有其他列
- ✅ 验证通过

### 测试2：不符合新规则的文件  
- ❌ 缺少必需列头：开始时间、结束时间
- ✅ 正确检测到错误并报告

## 影响范围

这次修改主要影响：

1. **向导页面生成的Excel文件验证**
   - 现在只需要包含4个必需列头
   - 内容可以为空，更加灵活

2. **Excel导入功能**
   - 导入的Excel文件只需要包含必需的列头
   - 数据验证更加宽松

3. **兼容性**
   - 保持向后兼容
   - 旧格式的文件仍然可以通过验证

## 注意事项

1. **时间格式支持**：
   - `HH:MM` 格式（如：09:00）
   - `yyyy/mm/dd hh:mm` 格式（如：2025/01/15 09:00）

2. **空值处理**：
   - 空值、NaN、空字符串都被视为有效的空值
   - 只对有实际内容的字段进行格式验证

3. **重叠检查**：
   - 只对有效的时间段进行重叠检查
   - 空值不参与重叠检查

## 总结

修改成功实现了用户要求的验证规则：
- ✅ 必须包含指定的4个列头
- ✅ 内容允许为空值  
- ✅ 可以有其他列
- ✅ 保持系统稳定性和兼容性
