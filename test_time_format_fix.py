#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试时间格式修复
验证向导页面生成的Excel文件是否符合新的验证规则
"""

import pandas as pd
import tempfile
import os
from datetime import datetime
from extended_validator import ExtendedExcelValidator

def create_test_wizard_data():
    """创建测试用的向导数据"""
    return {
        'subjects': [
            {
                'subject_code': 'A',
                'subject_name': '语文',
                'start_time': '2025/02/08 09:00',
                'end_time': '2025/02/08 11:30'
            },
            {
                'subject_code': 'B',
                'subject_name': '数学',
                'start_time': '2025/02/08 14:00',
                'end_time': '2025/02/08 16:00'
            },
            {
                'subject_code': 'C',
                'subject_name': '英语',
                'start_time': '2025/02/08 16:30',
                'end_time': '2025/02/08 18:30'
            },
            {
                'subject_code': 'D',
                'subject_name': '物理',
                'start_time': '2025/02/09 16:30',
                'end_time': '2025/02/09 18:30'
            },
            {
                'subject_code': 'E',
                'subject_name': '化学',
                'start_time': '2025/02/10 16:30',
                'end_time': '2025/02/10 18:30'
            }
        ],
        'rooms': [
            {
                'name': 'A101',
                'demands': {'语文': 2, '数学': 2, '英语': 1, '物理': 1, '化学': 1}
            },
            {
                'name': 'A102',
                'demands': {'语文': 1, '数学': 1, '英语': 2, '物理': 2, '化学': 2}
            }
        ],
        'proctors': [
            {
                'name': '张老师',
                'teaching_subject': '语文',
                'session_limit': 5,
                'required_subjects': '',
                'forbidden_subjects': '',
                'required_rooms': '',
                'forbidden_rooms': ''
            },
            {
                'name': '李老师',
                'teaching_subject': '数学',
                'session_limit': 5,
                'required_subjects': '',
                'forbidden_subjects': '',
                'required_rooms': '',
                'forbidden_rooms': ''
            },
            {
                'name': '王老师',
                'teaching_subject': '英语',
                'session_limit': 5,
                'required_subjects': '',
                'forbidden_subjects': '',
                'required_rooms': '',
                'forbidden_rooms': ''
            },
            {
                'name': '赵老师',
                'teaching_subject': '物理',
                'session_limit': 5,
                'required_subjects': '',
                'forbidden_subjects': '',
                'required_rooms': '',
                'forbidden_rooms': ''
            },
            {
                'name': '刘老师',
                'teaching_subject': '化学',
                'session_limit': 5,
                'required_subjects': '',
                'forbidden_subjects': '',
                'required_rooms': '',
                'forbidden_rooms': ''
            }
        ]
    }

def generate_excel_from_wizard_data_fixed(wizard_data, file_path):
    """修复后的Excel生成函数"""
    from datetime import datetime
    import pandas as pd

    # 1. 考试科目设置表 - 使用新的时间格式
    subjects_data = []
    for subject in wizard_data.get('subjects', []):
        start_time_str = subject.get('start_time', '')
        end_time_str = subject.get('end_time', '')

        # 解析时间字符串
        start_datetime = None
        end_datetime = None

        try:
            if start_time_str:
                start_datetime = datetime.strptime(start_time_str.strip(), '%Y/%m/%d %H:%M')
        except Exception as e:
            print(f"解析开始时间出错: {e}")
            start_datetime = datetime(2025, 2, 8, 9, 0)

        try:
            if end_time_str:
                end_datetime = datetime.strptime(end_time_str.strip(), '%Y/%m/%d %H:%M')
        except Exception as e:
            print(f"解析结束时间出错: {e}")
            end_datetime = datetime(2025, 2, 8, 11, 0)

        if not start_datetime:
            start_datetime = datetime(2025, 2, 8, 9, 0)
        if not end_datetime:
            end_datetime = datetime(2025, 2, 8, 11, 0)

        # 生成Excel数据行 - 使用新的验证规则格式
        subjects_data.append({
            '课程代码': subject.get('subject_code', '').strip(),
            '课程名称': subject.get('subject_name', '').strip(),
            '考试科目': subject.get('subject_name', '').strip(),  # 保留兼容性
            '考试日期': start_datetime.strftime('%Y/%m/%d'),  # 保留兼容性
            '开始时间': start_datetime.strftime('%Y/%m/%d %H:%M'),  # 新验证规则要求的格式
            '结束时间': end_datetime.strftime('%Y/%m/%d %H:%M')    # 新验证规则要求的格式
        })

    # 创建DataFrame并保存为Excel
    df_subjects = pd.DataFrame(subjects_data)

    # 创建Excel文件
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        # 写入考试科目设置表
        df_subjects.to_excel(writer, sheet_name='考试科目设置', index=False)

        # 2. 监考员设置表
        proctors_data = []
        for i, proctor in enumerate(wizard_data.get('proctors', []), 1):
            session_limit = proctor.get('session_limit', 99)
            try:
                session_limit = int(session_limit)
                if session_limit <= 0:
                    session_limit = 99
            except (ValueError, TypeError):
                session_limit = 99

            proctors_data.append({
                '序号': i,
                '监考老师': proctor.get('name', '').strip(),
                '任教科目': proctor.get('teaching_subject', ''),
                '必监考科目': proctor.get('required_subjects', ''),
                '不监考科目': proctor.get('forbidden_subjects', ''),
                '必监考考场': proctor.get('required_rooms', ''),
                '不监考考场': proctor.get('forbidden_rooms', ''),
                '场次限制': session_limit
            })

        df_proctors = pd.DataFrame(proctors_data)
        df_proctors.to_excel(writer, sheet_name='监考员设置', index=False)

        # 3. 考场设置表
        rooms_data = []
        subject_names = [s['subject_name'].strip() for s in wizard_data.get('subjects', [])]

        for room in wizard_data.get('rooms', []):
            room_data = {'考场': room.get('name', '').strip()}
            demands = room.get('demands', {})

            for subj in subject_names:
                try:
                    demand = int(demands.get(subj, 0))
                    if demand < 0:
                        demand = 0
                except (ValueError, TypeError):
                    demand = 0
                room_data[subj] = demand

            rooms_data.append(room_data)

        df_rooms = pd.DataFrame(rooms_data)
        df_rooms.to_excel(writer, sheet_name='考场设置', index=False)

def test_time_format_fix():
    """测试时间格式修复"""
    print("=== 测试时间格式修复 ===\n")
    
    # 创建测试数据
    wizard_data = create_test_wizard_data()
    
    # 生成测试文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    test_file = temp_file.name
    temp_file.close()
    
    try:
        # 使用修复后的函数生成Excel文件
        generate_excel_from_wizard_data_fixed(wizard_data, test_file)
        
        # 检查生成的文件内容
        df = pd.read_excel(test_file, sheet_name='考试科目设置')
        print("生成的考试科目设置表:")
        print("列名:", list(df.columns))
        print("\n数据:")
        for i, row in df.iterrows():
            print(f"{i+1}\t{row['课程代码']}\t{row['课程名称']}\t{row['开始时间']}\t{row['结束时间']}")
        
        # 验证时间格式
        print("\n时间格式验证:")
        for i, row in df.iterrows():
            start_time = str(row['开始时间'])
            end_time = str(row['结束时间'])
            print(f"  {row['课程名称']}: 开始={start_time}, 结束={end_time}")
            
            # 检查格式是否为 yyyy/mm/dd hh:mm
            try:
                datetime.strptime(start_time, '%Y/%m/%d %H:%M')
                print(f"    ✅ 开始时间格式正确")
            except ValueError:
                print(f"    ❌ 开始时间格式错误")
            
            try:
                datetime.strptime(end_time, '%Y/%m/%d %H:%M')
                print(f"    ✅ 结束时间格式正确")
            except ValueError:
                print(f"    ❌ 结束时间格式错误")
        
        # 使用验证器验证
        print("\n=== 验证器测试 ===")
        validator = ExtendedExcelValidator(test_file)
        result = validator.validate()
        
        print(f"验证结果: {'通过' if result else '失败'}")
        
        # 检查考试科目设置相关的错误
        subject_errors = [error for error in validator.errors if '考试科目设置' in error]
        if subject_errors:
            print("考试科目设置相关错误:")
            for error in subject_errors:
                print(f"  - {error}")
        else:
            print("✅ 考试科目设置验证通过")
        
        return len(subject_errors) == 0
        
    except Exception as e:
        print(f"测试出错: {e}")
        return False
    finally:
        try:
            if os.path.exists(test_file):
                os.unlink(test_file)
        except:
            pass

def main():
    """主函数"""
    success = test_time_format_fix()
    
    print("\n=== 修复总结 ===")
    if success:
        print("✅ 时间格式修复成功！")
        print("现在向导页面生成的Excel文件使用 yyyy/mm/dd hh:mm 格式")
        print("符合新的验证规则要求")
    else:
        print("❌ 时间格式修复失败")
        print("需要进一步检查和修复")

if __name__ == '__main__':
    main()
