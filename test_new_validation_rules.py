#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的考试科目设置验证规则
验证规则：
1. 必须包含表头：课程代码、课程名称、开始时间、结束时间
2. 内容允许为空值
3. 可以有其他列
"""

import pandas as pd
import tempfile
import os
from extended_validator import ExtendedExcelValidator

def create_test_excel_valid():
    """创建符合新验证规则的测试Excel文件"""
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 符合新规则：包含必需列头，内容允许为空
    subjects_data = {
        '课程代码': ['A001', '', 'C003'],  # 允许空值
        '课程名称': ['高等数学', '大学物理', ''],  # 允许空值
        '开始时间': ['2025/01/15 09:00', '', '2025/01/15 15:00'],  # 允许空值
        '结束时间': ['2025/01/15 11:00', '', '2025/01/15 17:00'],  # 允许空值
        '其他列': ['额外信息1', '额外信息2', '额外信息3']  # 允许其他列
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101', 'A102'],
        '高等数学': [1, 1],
        '大学物理': [1, 1]
    }

    # 监考员设置表 - 增加足够的监考员和场次限制
    proctors_data = {
        '序号': [1, 2, 3, 4],
        '监考老师': ['张老师', '李老师', '王老师', '赵老师'],
        '任教科目': ['数学', '物理', '数学', '物理'],
        '必监考科目': ['', '', '', ''],
        '不监考科目': ['', '', '', ''],
        '必监考考场': ['', '', '', ''],
        '不监考考场': ['', '', '', ''],
        '场次限制': [3, 3, 3, 3]
    }
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def create_test_excel_invalid():
    """创建不符合新验证规则的测试Excel文件（缺少必需列头）"""
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 缺少必需列头
    subjects_data = {
        '课程代码': ['A001', 'B002'],
        '课程名称': ['高等数学', '大学物理'],
        # 缺少 '开始时间' 和 '结束时间' 列
        '其他列': ['额外信息1', '额外信息2']
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101', 'A102'],
        '高等数学': [2, 1],
        '大学物理': [1, 2]
    }
    
    # 监考员设置表 - 增加足够的监考员和场次限制
    proctors_data = {
        '序号': [1, 2, 3, 4],
        '监考老师': ['张老师', '李老师', '王老师', '赵老师'],
        '任教科目': ['数学', '物理', '数学', '物理'],
        '必监考科目': ['', '', '', ''],
        '不监考科目': ['', '', '', ''],
        '必监考考场': ['', '', '', ''],
        '不监考考场': ['', '', '', ''],
        '场次限制': [3, 3, 3, 3]
    }
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def test_validation_rules():
    """测试新的验证规则"""
    print("=== 测试新的考试科目设置验证规则 ===\n")
    
    # 测试1：符合新规则的文件
    print("测试1：符合新规则的文件（包含必需列头，内容允许为空）")
    valid_file = create_test_excel_valid()
    
    try:
        validator = ExtendedExcelValidator(valid_file)
        result = validator.validate()

        print(f"验证结果: {'通过' if result else '失败'}")

        # 检查是否有考试科目设置相关的错误
        subject_errors = [error for error in validator.errors if '考试科目设置' in error]
        if subject_errors:
            print("考试科目设置相关错误:")
            for error in subject_errors:
                print(f"  - {error}")
        else:
            print("✅ 考试科目设置验证通过")

        # 检查文件内容
        df = pd.read_excel(valid_file, sheet_name='考试科目设置')
        print(f"\n文件内容:")
        print(f"列名: {list(df.columns)}")
        print(f"数据:\n{df}")

    except Exception as e:
        print(f"测试1出错: {e}")
    finally:
        try:
            if os.path.exists(valid_file):
                os.unlink(valid_file)
        except:
            pass
    
    print("\n" + "="*50 + "\n")
    
    # 测试2：不符合新规则的文件
    print("测试2：不符合新规则的文件（缺少必需列头）")
    invalid_file = create_test_excel_invalid()
    
    try:
        validator = ExtendedExcelValidator(invalid_file)
        result = validator.validate()

        print(f"验证结果: {'通过' if result else '失败'}")

        # 检查是否有考试科目设置相关的错误
        subject_errors = [error for error in validator.errors if '考试科目设置' in error]
        if subject_errors:
            print("考试科目设置相关错误:")
            for error in subject_errors:
                print(f"  - {error}")
            print("✅ 正确检测到缺少必需列头的错误")
        else:
            print("❌ 未检测到预期的错误")

        # 检查文件内容
        df = pd.read_excel(invalid_file, sheet_name='考试科目设置')
        print(f"\n文件内容:")
        print(f"列名: {list(df.columns)}")
        print(f"数据:\n{df}")

    except Exception as e:
        print(f"测试2出错: {e}")
    finally:
        try:
            if os.path.exists(invalid_file):
                os.unlink(invalid_file)
        except:
            pass

def main():
    """主函数"""
    test_validation_rules()
    
    print("\n=== 验证规则总结 ===")
    print("新的考试科目设置验证规则：")
    print("1. ✅ 必须包含表头：课程代码、课程名称、开始时间、结束时间")
    print("2. ✅ 内容允许为空值")
    print("3. ✅ 可以有其他列")
    print("4. ✅ 仅对非空时间值进行格式验证")
    print("5. ✅ 仅对有效时间进行重叠检查")

if __name__ == '__main__':
    main()
