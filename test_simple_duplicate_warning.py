#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试重复监考员警告功能
"""

import pandas as pd
import tempfile
import os
from extended_validator import ExtendedExcelValidator

def create_valid_excel_with_duplicates():
    """创建完全有效但包含重复监考员的Excel文件"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 完全符合要求
    subjects_data = {
        '课程代码': ['A', 'B', 'C'],
        '课程名称': ['语文', '数学', '英语'],
        '考试科目': ['语文', '数学', '英语'],  # 添加兼容性列
        '考试日期': ['2025/02/08', '2025/02/08', '2025/02/08'],  # 添加兼容性列
        '开始时间': ['2025/02/08 09:00', '2025/02/08 14:00', '2025/02/08 16:30'],
        '结束时间': ['2025/02/08 11:30', '2025/02/08 16:00', '2025/02/08 18:30']
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101', 'A102'],
        '语文': [1, 1],
        '数学': [1, 1],
        '英语': [1, 1]
    }
    
    # 监考员设置表 - 包含重复监考员，但其他都正确
    proctors_data = {
        '序号': [1, 2, 3, 4, 5],
        '监考老师': ['张老师', '李老师', '张老师', '王老师', '李老师'],  # 重复
        '任教科目': ['语文', '数学', '英语', '语文', '数学'],
        '必监考科目': ['', '', '', '', ''],
        '不监考科目': ['', '', '', '', ''],
        '必监考考场': ['', '', '', '', ''],
        '不监考考场': ['', '', '', '', ''],
        '场次限制': [3, 3, 3, 3, 3]  # 总计15，足够
    }
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def test_duplicate_warning():
    """测试重复监考员警告"""
    print("=== 测试重复监考员警告功能 ===\n")
    
    test_file = create_valid_excel_with_duplicates()
    
    try:
        print("创建的测试数据:")
        df = pd.read_excel(test_file, sheet_name='监考员设置')
        print("监考老师列:", list(df['监考老师']))
        
        # 手动检查重复
        duplicates = df[df['监考老师'].duplicated()]['监考老师'].unique()
        print("预期重复的监考员:", list(duplicates))
        
        print("\n开始验证...")
        validator = ExtendedExcelValidator(test_file)
        result = validator.validate()
        
        print(f"\n验证结果: {'通过' if result else '失败'}")
        
        print(f"\n错误数量: {len(validator.errors)}")
        if validator.errors:
            print("错误列表:")
            for i, error in enumerate(validator.errors, 1):
                print(f"  {i}. {error}")
        
        print(f"\n警告数量: {len(validator.warnings)}")
        if validator.warnings:
            print("警告列表:")
            for i, warning in enumerate(validator.warnings, 1):
                print(f"  {i}. {warning}")
        
        # 检查重复监考员警告
        duplicate_warnings = [w for w in validator.warnings if '重复的监考员' in w]
        duplicate_errors = [e for e in validator.errors if '重复的监考员' in e]
        
        print(f"\n重复监考员相关:")
        print(f"  警告: {len(duplicate_warnings)} 个")
        print(f"  错误: {len(duplicate_errors)} 个")
        
        if duplicate_warnings:
            print("✅ 重复监考员正确作为警告处理")
            for warning in duplicate_warnings:
                print(f"    {warning}")
        else:
            print("❌ 未检测到重复监考员警告")
        
        if duplicate_errors:
            print("❌ 重复监考员仍然作为错误处理")
            for error in duplicate_errors:
                print(f"    {error}")
        else:
            print("✅ 重复监考员不再作为错误处理")
        
        return len(duplicate_warnings) > 0 and len(duplicate_errors) == 0
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            if os.path.exists(test_file):
                os.unlink(test_file)
        except:
            pass

def main():
    """主函数"""
    success = test_duplicate_warning()
    
    print(f"\n=== 测试结果 ===")
    if success:
        print("✅ 重复监考员警告功能正常工作")
    else:
        print("❌ 重复监考员警告功能需要进一步检查")

if __name__ == '__main__':
    main()
