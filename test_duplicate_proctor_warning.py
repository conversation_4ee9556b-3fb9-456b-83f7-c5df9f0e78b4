#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重复监考员警告功能
验证重复监考员现在是作为警告而不是错误处理
"""

import pandas as pd
import tempfile
import os
from extended_validator import ExtendedExcelValidator

def create_test_excel_with_duplicates():
    """创建包含重复监考员的测试Excel文件"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 总共3个科目
    subjects_data = {
        '课程代码': ['A', 'B', 'C'],
        '课程名称': ['语文', '数学', '英语'],
        '开始时间': ['2025/02/08 09:00', '2025/02/08 14:00', '2025/02/08 16:30'],
        '结束时间': ['2025/02/08 11:30', '2025/02/08 16:00', '2025/02/08 18:30']
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101', 'A102'],
        '语文': [1, 1],
        '数学': [1, 1],
        '英语': [1, 1]
    }
    
    # 监考员设置表 - 包含重复的监考员
    proctors_data = {
        '序号': [1, 2, 3, 4, 5],
        '监考老师': ['张老师', '李老师', '张老师', '王老师', '李老师'],  # 张老师和李老师重复
        '任教科目': ['语文', '数学', '英语', '语文', '数学'],
        '必监考科目': ['', '', '', '', ''],
        '不监考科目': ['', '', '', '', ''],
        '必监考考场': ['', '', '', '', ''],
        '不监考考场': ['', '', '', '', ''],
        '场次限制': [3, 3, 3, 3, 3]  # 总计15，足够
    }
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def create_test_excel_without_duplicates():
    """创建不包含重复监考员的测试Excel文件"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 总共3个科目
    subjects_data = {
        '课程代码': ['A', 'B', 'C'],
        '课程名称': ['语文', '数学', '英语'],
        '开始时间': ['2025/02/08 09:00', '2025/02/08 14:00', '2025/02/08 16:30'],
        '结束时间': ['2025/02/08 11:30', '2025/02/08 16:00', '2025/02/08 18:30']
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101', 'A102'],
        '语文': [1, 1],
        '数学': [1, 1],
        '英语': [1, 1]
    }
    
    # 监考员设置表 - 没有重复的监考员
    proctors_data = {
        '序号': [1, 2, 3, 4, 5],
        '监考老师': ['张老师', '李老师', '王老师', '赵老师', '刘老师'],  # 没有重复
        '任教科目': ['语文', '数学', '英语', '语文', '数学'],
        '必监考科目': ['', '', '', '', ''],
        '不监考科目': ['', '', '', '', ''],
        '必监考考场': ['', '', '', '', ''],
        '不监考考场': ['', '', '', '', ''],
        '场次限制': [3, 3, 3, 3, 3]  # 总计15，足够
    }
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def test_duplicate_proctor_warning():
    """测试重复监考员警告功能"""
    print("=== 测试重复监考员警告功能 ===\n")
    
    # 测试案例1：包含重复监考员
    print("测试案例1：包含重复监考员的数据")
    duplicate_file = create_test_excel_with_duplicates()
    
    try:
        validator = ExtendedExcelValidator(duplicate_file)
        result = validator.validate()
        
        print(f"验证结果: {'通过' if result else '失败'}")
        
        # 检查是否有重复监考员的警告
        duplicate_warnings = [warning for warning in validator.warnings if '重复的监考员' in warning]
        if duplicate_warnings:
            print("✅ 正确检测到重复监考员警告:")
            for warning in duplicate_warnings:
                print(f"  警告: {warning}")
        else:
            print("❌ 未检测到重复监考员警告")
        
        # 检查是否有重复监考员的错误（应该没有）
        duplicate_errors = [error for error in validator.errors if '重复的监考员' in error]
        if duplicate_errors:
            print("❌ 错误：重复监考员仍然作为错误处理:")
            for error in duplicate_errors:
                print(f"  错误: {error}")
        else:
            print("✅ 重复监考员不再作为错误处理")
        
        # 显示监考员数据
        df = pd.read_excel(duplicate_file, sheet_name='监考员设置')
        print(f"\n监考员数据:")
        print("监考老师列:", list(df['监考老师']))
        
    except Exception as e:
        print(f"测试案例1出错: {e}")
    finally:
        try:
            if os.path.exists(duplicate_file):
                os.unlink(duplicate_file)
        except:
            pass
    
    print("\n" + "="*50 + "\n")
    
    # 测试案例2：不包含重复监考员
    print("测试案例2：不包含重复监考员的数据")
    no_duplicate_file = create_test_excel_without_duplicates()
    
    try:
        validator = ExtendedExcelValidator(no_duplicate_file)
        result = validator.validate()
        
        print(f"验证结果: {'通过' if result else '失败'}")
        
        # 检查是否有重复监考员的警告（应该没有）
        duplicate_warnings = [warning for warning in validator.warnings if '重复的监考员' in warning]
        if duplicate_warnings:
            print("❌ 错误：检测到不应该存在的重复监考员警告:")
            for warning in duplicate_warnings:
                print(f"  警告: {warning}")
        else:
            print("✅ 没有重复监考员警告（正确）")
        
        # 显示监考员数据
        df = pd.read_excel(no_duplicate_file, sheet_name='监考员设置')
        print(f"\n监考员数据:")
        print("监考老师列:", list(df['监考老师']))
        
    except Exception as e:
        print(f"测试案例2出错: {e}")
    finally:
        try:
            if os.path.exists(no_duplicate_file):
                os.unlink(no_duplicate_file)
        except:
            pass

def test_validation_still_works():
    """测试验证功能仍然正常工作"""
    print("\n" + "="*50 + "\n")
    print("测试案例3：验证其他验证功能仍然正常工作")
    
    # 创建一个有其他错误但有重复监考员的文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 缺少必需列头（会导致错误）
    subjects_data = {
        '课程代码': ['A', 'B'],
        '课程名称': ['语文', '数学'],
        # 缺少 '开始时间' 和 '结束时间' 列
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101'],
        '语文': [1],
        '数学': [1]
    }
    
    # 监考员设置表 - 包含重复监考员
    proctors_data = {
        '序号': [1, 2, 3],
        '监考老师': ['张老师', '张老师', '李老师'],  # 张老师重复
        '任教科目': ['语文', '数学', '英语'],
        '必监考科目': ['', '', ''],
        '不监考科目': ['', '', ''],
        '必监考考场': ['', '', ''],
        '不监考考场': ['', '', ''],
        '场次限制': [5, 5, 5]
    }
    
    try:
        with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
            pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
            pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
            pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
        
        validator = ExtendedExcelValidator(temp_file.name)
        result = validator.validate()
        
        print(f"验证结果: {'通过' if result else '失败'}")
        
        # 应该有其他错误
        other_errors = [error for error in validator.errors if '重复的监考员' not in error]
        if other_errors:
            print("✅ 其他验证错误仍然正常检测:")
            for error in other_errors[:3]:  # 只显示前3个错误
                print(f"  错误: {error}")
            if len(other_errors) > 3:
                print(f"  ... 还有 {len(other_errors) - 3} 个其他错误")
        else:
            print("❌ 其他验证功能可能有问题")
        
        # 应该有重复监考员警告
        duplicate_warnings = [warning for warning in validator.warnings if '重复的监考员' in warning]
        if duplicate_warnings:
            print("✅ 重复监考员警告正常:")
            for warning in duplicate_warnings:
                print(f"  警告: {warning}")
        else:
            print("❌ 重复监考员警告未检测到")
        
    except Exception as e:
        print(f"测试案例3出错: {e}")
    finally:
        try:
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
        except:
            pass

def main():
    """主函数"""
    test_duplicate_proctor_warning()
    test_validation_still_works()
    
    print("\n=== 重复监考员警告功能更新总结 ===")
    print("✅ 重复监考员现在作为警告处理，不再阻止验证通过")
    print("✅ 其他验证功能仍然正常工作")
    print("✅ 验证结果更加用户友好")

if __name__ == '__main__':
    main()
