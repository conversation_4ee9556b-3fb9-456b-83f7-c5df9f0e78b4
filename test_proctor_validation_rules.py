#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试监考员设置验证规则更新
新规则：
1. 删除：同一个监考员必监考科目的数量加场次限制的总和不能超过考试所有科目的总和
2. 删除：同一个监考员必监考科目的数量和不监考科目的数量不能超过考试所有科目的总和
3. 添加：同一个监考员必监考科目的数量+不监考科目的数量之和不能超过考试所有科目的总和
4. 添加：同一个监考员必监考科目的数量小于等于场次限制的数量
"""

import pandas as pd
import tempfile
import os
from extended_validator import ExtendedExcelValidator

def create_test_excel_case1():
    """测试案例1：符合新规则的数据"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 总共3个科目
    subjects_data = {
        '课程代码': ['A', 'B', 'C'],
        '课程名称': ['语文', '数学', '英语'],
        '开始时间': ['2025/02/08 09:00', '2025/02/08 14:00', '2025/02/08 16:30'],
        '结束时间': ['2025/02/08 11:30', '2025/02/08 16:00', '2025/02/08 18:30']
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101', 'A102'],
        '语文': [1, 1],
        '数学': [1, 1],
        '英语': [1, 1]
    }
    
    # 监考员设置表 - 符合新规则，确保总场次限制足够
    proctors_data = {
        '序号': [1, 2, 3, 4],
        '监考老师': ['张老师', '李老师', '王老师', '赵老师'],
        '任教科目': ['语文', '数学', '英语', '语文'],
        '必监考科目': ['语文', '数学', '', ''],  # 必监考科目数量：1, 1, 0, 0
        '不监考科目': ['', '英语', '', ''],     # 不监考科目数量：0, 1, 0, 0
        '必监考考场': ['', '', '', ''],
        '不监考考场': ['', '', '', ''],
        '场次限制': [2, 3, 3, 3]              # 场次限制：2, 3, 3, 3 总计11
    }
    # 张老师：必监考(1) + 不监考(0) = 1 <= 3 ✓, 必监考(1) <= 场次限制(2) ✓
    # 李老师：必监考(1) + 不监考(1) = 2 <= 3 ✓, 必监考(1) <= 场次限制(3) ✓
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def create_test_excel_case2():
    """测试案例2：违反新规则1 - 必监考+不监考科目数量超过总科目数"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 总共3个科目
    subjects_data = {
        '课程代码': ['A', 'B', 'C'],
        '课程名称': ['语文', '数学', '英语'],
        '开始时间': ['2025/02/08 09:00', '2025/02/08 14:00', '2025/02/08 16:30'],
        '结束时间': ['2025/02/08 11:30', '2025/02/08 16:00', '2025/02/08 18:30']
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101', 'A102'],
        '语文': [1, 1],
        '数学': [1, 1],
        '英语': [1, 1]
    }
    
    # 监考员设置表 - 违反规则1，确保总场次限制足够
    proctors_data = {
        '序号': [1, 2, 3, 4, 5],
        '监考老师': ['张老师', '李老师', '王老师', '赵老师', '刘老师'],
        '任教科目': ['语文', '数学', '英语', '语文', '数学'],
        '必监考科目': ['语文,数学', '', '', '', ''],    # 必监考科目数量：2, 0, 0, 0, 0
        '不监考科目': ['英语,物理', '', '', '', ''],    # 不监考科目数量：2 (物理不存在，但仍计数)
        '必监考考场': ['', '', '', '', ''],
        '不监考考场': ['', '', '', '', ''],
        '场次限制': [5, 3, 3, 3, 3]                    # 总计17，足够
    }
    # 张老师：必监考(2) + 不监考(2) = 4 > 3 ❌ 违反规则1
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def create_test_excel_case3():
    """测试案例3：违反新规则2 - 必监考科目数量大于场次限制"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    
    # 考试科目设置表 - 总共3个科目
    subjects_data = {
        '课程代码': ['A', 'B', 'C'],
        '课程名称': ['语文', '数学', '英语'],
        '开始时间': ['2025/02/08 09:00', '2025/02/08 14:00', '2025/02/08 16:30'],
        '结束时间': ['2025/02/08 11:30', '2025/02/08 16:00', '2025/02/08 18:30']
    }
    
    # 考场设置表
    rooms_data = {
        '考场': ['A101', 'A102'],
        '语文': [1, 1],
        '数学': [1, 1],
        '英语': [1, 1]
    }
    
    # 监考员设置表 - 违反规则2，确保总场次限制足够
    proctors_data = {
        '序号': [1, 2, 3, 4, 5],
        '监考老师': ['张老师', '李老师', '王老师', '赵老师', '刘老师'],
        '任教科目': ['语文', '数学', '英语', '语文', '数学'],
        '必监考科目': ['语文,数学,英语', '', '', '', ''],  # 必监考科目数量：3, 0, 0, 0, 0
        '不监考科目': ['', '', '', '', ''],              # 不监考科目数量：0, 0, 0, 0, 0
        '必监考考场': ['', '', '', '', ''],
        '不监考考场': ['', '', '', '', ''],
        '场次限制': [2, 5, 5, 5, 5]                      # 场次限制：2, 5, 5, 5, 5 总计22
    }
    # 张老师：必监考(3) > 场次限制(2) ❌ 违反规则2
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='考试科目设置', index=False)
        pd.DataFrame(rooms_data).to_excel(writer, sheet_name='考场设置', index=False)
        pd.DataFrame(proctors_data).to_excel(writer, sheet_name='监考员设置', index=False)
    
    return temp_file.name

def test_validation_rules():
    """测试新的验证规则"""
    print("=== 测试监考员设置验证规则更新 ===\n")
    
    # 测试案例1：符合新规则
    print("测试案例1：符合新规则的数据")
    case1_file = create_test_excel_case1()
    
    try:
        validator = ExtendedExcelValidator(case1_file)
        result = validator.validate()
        
        print(f"验证结果: {'通过' if result else '失败'}")
        
        # 检查监考员设置相关的错误
        proctor_errors = [error for error in validator.errors if '监考员设置' in error and ('必监考科目' in error or '场次限制' in error)]
        if proctor_errors:
            print("监考员设置相关错误:")
            for error in proctor_errors:
                print(f"  - {error}")
        else:
            print("✅ 监考员设置验证通过")
        
    except Exception as e:
        print(f"测试案例1出错: {e}")
    finally:
        try:
            if os.path.exists(case1_file):
                os.unlink(case1_file)
        except:
            pass
    
    print("\n" + "="*50 + "\n")
    
    # 测试案例2：违反规则1
    print("测试案例2：违反规则1 - 必监考+不监考科目数量超过总科目数")
    case2_file = create_test_excel_case2()
    
    try:
        validator = ExtendedExcelValidator(case2_file)
        result = validator.validate()
        
        print(f"验证结果: {'通过' if result else '失败'}")
        
        # 检查是否检测到规则1的违反
        rule1_errors = [error for error in validator.errors if '必监考科目数量' in error and '+不监考科目数量' in error and '之和' in error]
        if rule1_errors:
            print("✅ 正确检测到规则1违反:")
            for error in rule1_errors:
                print(f"  - {error}")
        else:
            print("❌ 未检测到规则1违反")
        
    except Exception as e:
        print(f"测试案例2出错: {e}")
    finally:
        try:
            if os.path.exists(case2_file):
                os.unlink(case2_file)
        except:
            pass
    
    print("\n" + "="*50 + "\n")
    
    # 测试案例3：违反规则2
    print("测试案例3：违反规则2 - 必监考科目数量大于场次限制")
    case3_file = create_test_excel_case3()
    
    try:
        validator = ExtendedExcelValidator(case3_file)
        result = validator.validate()
        
        print(f"验证结果: {'通过' if result else '失败'}")
        
        # 检查是否检测到规则2的违反
        rule2_errors = [error for error in validator.errors if '必监考科目数量' in error and '不能大于场次限制数量' in error]
        if rule2_errors:
            print("✅ 正确检测到规则2违反:")
            for error in rule2_errors:
                print(f"  - {error}")
        else:
            print("❌ 未检测到规则2违反")
        
    except Exception as e:
        print(f"测试案例3出错: {e}")
    finally:
        try:
            if os.path.exists(case3_file):
                os.unlink(case3_file)
        except:
            pass

def main():
    """主函数"""
    test_validation_rules()
    
    print("\n=== 验证规则更新总结 ===")
    print("新的监考员设置验证规则：")
    print("1. ✅ 删除：必监考科目数量加场次限制不能超过考试所有科目总和")
    print("2. ✅ 删除：必监考科目和不监考科目数量不能超过考试所有科目总和")
    print("3. ✅ 添加：必监考科目数量+不监考科目数量之和不能超过考试所有科目总和")
    print("4. ✅ 添加：必监考科目数量小于等于场次限制数量")

if __name__ == '__main__':
    main()
