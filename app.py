from flask import Flask, render_template, redirect, url_for, flash, request, jsonify, send_file, abort, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, current_user, login_required
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename  # 用于清理文件名中的非法字符
from datetime import datetime, timezone, timedelta
from flask_wtf.csrf import CSRFProtect
import os
import subprocess
import threading
import logging
import sys
import uuid
from excel_validator import ExcelValidator
from extended_validator import ExtendedExcelValidator
import time
from functools import wraps
from config import Config
import redis
import json
from concurrent.futures import ThreadPoolExecutor
import queue
from core_adapter import CoreAdapter
from openpyxl import Workbook

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('app.log')
    ]
)
logger = logging.getLogger('jiankao_app')

# 初始化Flask应用
app = Flask(__name__)

# 从配置类加载配置
app.config.from_object(Config)

# 设置SECRET_KEY
app.config['SECRET_KEY'] = Config.get_or_generate_secret_key()

# 初始化CSRF保护
csrf = CSRFProtect(app)

# 设置CSRF例外路由
@csrf.exempt
def csrf_exempt(view):
    return view

# 如果需要调试时禁用CSRF保护，可以使用以下代码
# csrf.exempt(schedule_task)

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 创建临时目录
tmp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'tmp')
os.makedirs(tmp_dir, exist_ok=True)

# 初始化Redis连接
try:
    redis_client = redis.Redis(
        host=Config.REDIS_HOST,
        port=Config.REDIS_PORT,
        db=Config.REDIS_DB,
        decode_responses=True,
        socket_timeout=5,  # 设置超时时间
        socket_connect_timeout=5,  # 设置连接超时
        retry_on_timeout=True,  # 超时时重试
        health_check_interval=30  # 定期检查连接健康状态
    )
    # 测试Redis连接
    redis_client.ping()
    logger.info("Redis连接成功")
except Exception as e:
    logger.error(f"Redis连接失败: {str(e)}")
    # 创建一个虚拟的Redis客户端，实现优雅降级
    from fakeredis import FakeStrictRedis
    redis_client = FakeStrictRedis(decode_responses=True)
    logger.warning("使用内存模拟的Redis客户端代替")

# 创建线程池和任务队列
MAX_WORKERS = 3
task_executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)
task_queue = queue.PriorityQueue()
processing_tasks = set()
queue_lock = threading.Lock()

# 创建各种锁和计数器
task_locks = {}
task_locks_lock = threading.Lock()  # 用于保护task_locks字典
db_lock = threading.RLock()  # 用于数据库操作
redis_lock = threading.RLock()  # 用于 Redis 操作
file_locks = {}  # 用于文件操作
file_locks_lock = threading.Lock()  # 用于保护file_locks字典

# 获取任务锁
def get_task_lock(task_id):
    with task_locks_lock:
        if task_id not in task_locks:
            task_locks[task_id] = threading.RLock()
        return task_locks[task_id]

# 获取文件锁
def get_file_lock(file_path):
    with file_locks_lock:
        if file_path not in file_locks:
            file_locks[file_path] = threading.RLock()
        return file_locks[file_path]

# 清理任务锁
def cleanup_task_lock(task_id):
    with task_locks_lock:
        if task_id in task_locks:
            del task_locks[task_id]

# 清理文件锁
def cleanup_file_lock(file_path):
    with file_locks_lock:
        if file_path in file_locks:
            del file_locks[file_path]

def get_validation_progress(validation_id):
    """从Redis获取验证进度"""
    progress = redis_client.get(f"validation_progress:{validation_id}")
    return int(progress) if progress else 0

def set_validation_progress(validation_id, progress):
    """设置验证进度到Redis"""
    redis_client.set(f"validation_progress:{validation_id}", progress)

def get_validation_result(validation_id):
    """从Redis获取验证结果"""
    result = redis_client.get(f"validation_result:{validation_id}")
    return json.loads(result) if result else None

def store_validation_result(validation_id, result):
    """存储验证结果，并设置过期时间"""
    logger.info(f"存储验证结果: {validation_id}")
    # 添加时间戳和过期时间
    result['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    result['expires_at'] = (datetime.now() + timedelta(minutes=30)).strftime('%Y-%m-%d %H:%M:%S.%f')

    redis_client.set(
        f"validation_result:{validation_id}",
        json.dumps(result),
        ex=1800  # 30分钟过期
    )
    logger.info(f"验证结果已存储，将在 {result['expires_at']} 过期")

def cleanup_validation_result(validation_id):
    """清理验证结果和相关资源"""
    redis_client.delete(f"validation_progress:{validation_id}")
    redis_client.delete(f"validation_result:{validation_id}")
    logger.info(f"已清理验证ID: {validation_id} 的相关数据")

def is_validation_result_valid(validation_id):
    """检查验证结果是否存在且未过期"""
    result = get_validation_result(validation_id)
    if not result:
        logger.warning(f"验证结果不存在: {validation_id}")
        return False

    if 'expires_at' not in result:
        logger.warning(f"验证结果缺少过期时间: {validation_id}")
        return False

    expires_at = datetime.strptime(result['expires_at'], '%Y-%m-%d %H:%M:%S.%f')
    is_valid = expires_at > datetime.now()
    if not is_valid:
        logger.warning(f"验证结果已过期: {validation_id}, 过期时间: {expires_at}")
    return is_valid

# 添加上下文处理器
@app.context_processor
def inject_now():
    return {'now': datetime.now()}

# 初始化数据库
db = SQLAlchemy(app)

# 初始化登录管理器
login_manager = LoginManager(app)
login_manager.login_view = 'login'

# 定义用户模型
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    role = db.Column(db.String(20), default='user')  # user, vip, admin
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    last_login = db.Column(db.DateTime)
    expiry_date = db.Column(db.DateTime)
    task_limit = db.Column(db.Integer, default=1)

    tasks = db.relationship('Task', backref='user', lazy='dynamic')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

# 定义任务模型
class Task(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')
    progress = db.Column(db.Integer, default=0)
    priority = db.Column(db.Integer, default=0)  # 0-普通优先级，1-VIP优先级
    input_file = db.Column(db.String(255))
    template_file = db.Column(db.String(255))  # 原始模板文件路径
    original_filename = db.Column(db.String(255))  # 原始文件名
    output_file = db.Column(db.String(255))
    error_message = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, onupdate=lambda: datetime.now(timezone.utc))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    @property
    def status_display(self):
        status_map = {
            'pending': '等待处理',
            'processing': '处理中',
            'completed': '已完成',
            'failed': '失败'
        }
        return status_map.get(self.status, self.status)

    @property
    def status_color(self):
        color_map = {
            'pending': 'secondary',
            'processing': 'primary',
            'completed': 'success',
            'failed': 'danger'
        }
        return color_map.get(self.status, 'secondary')

    @classmethod
    def get_next_task(cls):
        """获取下一个要处理的任务，优先处理VIP用户的任务"""
        return cls.query.filter_by(status='pending')\
            .order_by(cls.priority.desc(), cls.created_at.asc())\
            .first()

    def set_priority_by_user(self):
        """根据用户角色设置任务优先级"""
        if self.user.role in ['vip', 'admin']:
            self.priority = 1
        else:
            self.priority = 0

# 用户加载函数
@login_manager.user_loader
def load_user(id):
    return db.session.get(User, int(id))

# 管理员权限装饰器
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('需要管理员权限')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 检查文件扩展名是否允许
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# 路由：首页
@app.route('/')
def index():
    return render_template('index.html', title='首页')

# 路由：验证测试页面
@app.route('/validate-test')
@login_required
def validate_test():
    return render_template('validate_test.html', title='验证测试')

# 路由：登录
@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        remember = 'remember' in request.form

        user = User.query.filter_by(username=username).first()
        if user is None or not user.check_password(password):
            flash('用户名或密码错误')
            return redirect(url_for('login'))

        login_user(user, remember=remember)
        user.last_login = datetime.now(timezone.utc)
        db.session.commit()

        next_page = request.args.get('next')
        if not next_page or not next_page.startswith('/'):
            next_page = url_for('dashboard')
        return redirect(next_page)

    return render_template('login.html', title='登录')

# 路由：注册
@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']

        if User.query.filter_by(username=username).first():
            flash('用户名已存在')
            return redirect(url_for('register'))

        if User.query.filter_by(email=email).first():
            flash('邮箱已存在')
            return redirect(url_for('register'))

        user = User(username=username, email=email)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()

        flash('注册成功！请登录。')
        return redirect(url_for('login'))

    return render_template('register.html', title='注册')

# 路由：登出
@app.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('index'))

# 路由：控制台
@app.route('/dashboard')
@login_required
def dashboard():
    # 按状态和时间分组获取任务
    all_tasks = current_user.tasks.order_by(Task.created_at.desc()).all()
    
    # 分组任务
    processing_tasks = [t for t in all_tasks if t.status == 'processing']
    pending_tasks = [t for t in all_tasks if t.status == 'pending']
    completed_tasks = [t for t in all_tasks if t.status == 'completed']
    failed_tasks = [t for t in all_tasks if t.status == 'failed']
    
    # 构建智能显示的任务列表 - 按创建时间排序，最新的在前
    # 将所有重要任务合并并按创建时间重新排序
    important_tasks = []
    
    # 1. 所有进行中的任务（优先级最高）
    important_tasks.extend(processing_tasks)
    # 2. 所有待处理的任务
    important_tasks.extend(pending_tasks)
    # 3. 最新的3个已完成任务
    important_tasks.extend(completed_tasks[:3])
    # 4. 最新的2个失败任务
    important_tasks.extend(failed_tasks[:2])
    
    # 按创建时间倒序排列，确保最新的任务在最前面
    priority_tasks = sorted(important_tasks, key=lambda x: x.created_at, reverse=True)
    
    # 其余任务（用于"显示更多"）
    other_completed = completed_tasks[3:] if len(completed_tasks) > 3 else []
    other_failed = failed_tasks[2:] if len(failed_tasks) > 2 else []
    other_tasks = other_completed + other_failed
    
    # 计算剩余可创建任务数量
    current_task_count = len(all_tasks)
    remaining_tasks = current_user.task_limit - current_task_count if current_user.role != 'admin' else float('inf')
    
    return render_template('dashboard.html', 
                         title='控制台', 
                         priority_tasks=priority_tasks,
                         other_tasks=other_tasks,
                         task_counts={
                             'processing': len(processing_tasks),
                             'pending': len(pending_tasks),
                             'completed': len(completed_tasks),
                             'failed': len(failed_tasks),
                             'total': len(all_tasks)
                         },
                         remaining_tasks=remaining_tasks)

# 路由：任务管理页面（显示所有任务）
@app.route('/tasks')
@login_required 
def tasks_list():
    page = request.args.get('page', 1, type=int)
    per_page = 12  # 每页显示12个任务
    
    # 获取筛选条件
    status_filter = request.args.get('status', 'all')
    
    # 构建查询
    query = current_user.tasks
    if status_filter != 'all':
        query = query.filter(Task.status == status_filter)
    
    # 分页查询
    tasks = query.order_by(Task.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 任务统计
    task_counts = {
        'processing': current_user.tasks.filter(Task.status == 'processing').count(),
        'pending': current_user.tasks.filter(Task.status == 'pending').count(),
        'completed': current_user.tasks.filter(Task.status == 'completed').count(),
        'failed': current_user.tasks.filter(Task.status == 'failed').count(),
        'total': current_user.tasks.count()
    }
    
    return render_template('tasks_list.html', 
                         title='任务管理',
                         tasks=tasks,
                         task_counts=task_counts,
                         status_filter=status_filter)

# 路由：开始新的设置向导
@app.route('/wizard/start')
@login_required
def wizard_start():
    # 使用会话ID来隔离不同用户的向导数据
    session['wizard_id'] = str(uuid.uuid4())
    
    # 获取当前日期
    today = datetime.now()
    current_date = today.strftime('%Y-%m-%d')
    
    # 生成新的默认科目数据 - 使用新的日期时间格式
    current_date_formatted = today.strftime('%Y/%m/%d')  # 转换为 YYYY/MM/DD 格式
    default_subjects = [
        {
            'subject_code': 'A',
            'subject_name': '语文',
            'start_time': f'{current_date_formatted} 08:30',
            'end_time': f'{current_date_formatted} 10:30'
        },
        {
            'subject_code': 'B',
            'subject_name': '数学',
            'start_time': f'{current_date_formatted} 14:00',
            'end_time': f'{current_date_formatted} 16:00'
        },
        {
            'subject_code': 'C',
            'subject_name': '英语',
            'start_time': f'{current_date_formatted} 16:30',
            'end_time': f'{current_date_formatted} 18:30'
        }
    ]
    
    # 确保 wizard_data 是一个新的字典
    session['wizard_data'] = {'subjects': default_subjects}
    session.modified = True
    
    # 打印日志，帮助调试
    print("设置默认科目数据:", session['wizard_data'])
    print("Session ID:", session['wizard_id'])
    print("Session keys:", list(session.keys()))
    print("Session cookie name:", app.config['SESSION_COOKIE_NAME'])
    print("Session cookie secure:", app.config['SESSION_COOKIE_SECURE'])
    print("Session cookie httponly:", app.config['SESSION_COOKIE_HTTPONLY'])
    
    return redirect(url_for('wizard_step1_subjects'))

# 路由：向导第一步 - 考试科目设置
@app.route('/wizard/step1_subjects', methods=['GET', 'POST'])
@login_required
def wizard_step1_subjects():
    print("Session ID:", session.get('wizard_id'))
    print("Session keys:", list(session.keys()))
    print("Current wizard_data:", session.get('wizard_data'))
    print("Session cookie name:", app.config['SESSION_COOKIE_NAME'])
    print("Session cookie secure:", app.config['SESSION_COOKIE_SECURE'])
    print("Session cookie httponly:", app.config['SESSION_COOKIE_HTTPONLY'])
    
    if 'wizard_data' not in session:
        print("未找到 wizard_data，重定向到 wizard_start")
        return redirect(url_for('wizard_start'))

    if request.method == 'POST':
        subjects_data_str = request.form.get('subjects_data')
        if subjects_data_str:
            try:
                subjects = json.loads(subjects_data_str)
                # 数据清洗和验证
                validated_subjects = []
                for s in subjects:
                    # 支持新格式（只需要 subject_code, subject_name, start_time, end_time）
                    if (s.get('subject_code') and s.get('subject_name') and 
                        s.get('start_time') and s.get('end_time')):
                        
                        # 验证时间格式是否正确
                        start_time = s['start_time'].strip()
                        end_time = s['end_time'].strip()
                        
                        # 检查是否是新的日期时间格式 YYYY/MM/DD HH:MM
                        datetime_pattern = r'^\d{4}/\d{1,2}/\d{1,2} \d{1,2}:\d{2}$'
                        import re
                        
                        if re.match(datetime_pattern, start_time) and re.match(datetime_pattern, end_time):
                            # 新格式：直接保存
                            validated_subjects.append({
                                'subject_code': s['subject_code'].strip(),
                                'subject_name': s['subject_name'].strip(),
                                'start_time': start_time,
                                'end_time': end_time
                            })
                        else:
                            # 旧格式兼容：如果有exam_date字段，组合成新格式
                            exam_date = s.get('exam_date', '').strip()
                            if exam_date:
                                # 转换日期格式并组合
                                if '-' in exam_date:
                                    exam_date = exam_date.replace('-', '/')
                                
                                # 组合成完整的日期时间
                                full_start_time = f"{exam_date} {start_time}"
                                full_end_time = f"{exam_date} {end_time}"
                                
                                validated_subjects.append({
                                    'subject_code': s['subject_code'].strip(),
                                    'subject_name': s['subject_name'].strip(),
                                    'start_time': full_start_time,
                                    'end_time': full_end_time
                                })
                            else:
                                logger.warning(f"科目 {s.get('subject_name')} 的时间格式不正确，跳过")
                                continue
                    else:
                        logger.warning(f"科目数据不完整，跳过: {s}")
                        continue
                
                session['wizard_data']['subjects'] = validated_subjects
                session.modified = True
                logger.info(f"保存科目数据: {validated_subjects}")
                return redirect(url_for('wizard_step2_rooms'))
            except json.JSONDecodeError:
                flash('提交的数据格式错误', 'danger')
            except Exception as e:
                logger.error(f"处理科目数据时发生错误: {str(e)}")
                flash('处理数据时发生错误', 'danger')

    wizard_data = session.get('wizard_data', {})
    if 'subjects' not in wizard_data:
        # 如果没有科目数据，重定向到开始页面
        print("未找到科目数据，重定向到 wizard_start")
        return redirect(url_for('wizard_start'))
    
    print("渲染页面时的科目数据:", wizard_data)
        
    return render_template(
        'wizard/step1_subjects.html',
        title="向导第一步：考试科目设置",
        current_step=1,
        wizard_data=wizard_data
    )

# 路由：向导第二步 - 考场设置
@app.route('/wizard/step2_rooms', methods=['GET', 'POST'])
@login_required
def wizard_step2_rooms():
    if 'wizard_data' not in session or not session['wizard_data'].get('subjects'):
        flash('请先完成第一步：设置考试科目。', 'warning')
        return redirect(url_for('wizard_step1_subjects'))

    wizard_data = session.get('wizard_data', {})
    # 如果没有考场数据，则生成默认考场
    if 'rooms' not in wizard_data or not wizard_data['rooms']:
        default_rooms = [{'name': f'{i}考场', 'demands': {}} for i in range(1, 6)]
        wizard_data['rooms'] = default_rooms
        session['wizard_data'] = wizard_data
        session.modified = True
        
    if request.method == 'POST':
        rooms_data_str = request.form.get('rooms_data')
        if rooms_data_str:
            try:
                rooms = json.loads(rooms_data_str)
                # 数据清洗和验证
                validated_rooms = []
                for r in rooms:
                    if r.get('name'):
                        # 将需求中的空字符串或非数字值过滤掉
                        demands = {
                            k: int(v) 
                            for k, v in r.get('demands', {}).items() 
                            if v and str(v).isdigit()
                        }
                        validated_rooms.append({
                            'name': r['name'].strip(),
                            'demands': demands
                        })
                session['wizard_data']['rooms'] = validated_rooms
                session.modified = True
                return redirect(url_for('wizard_step3_proctors'))
            except (json.JSONDecodeError, ValueError):
                flash('提交的数据格式错误', 'danger')

    return render_template(
        'wizard/step2_rooms.html',
        title="向导第二步：考场与需求设置",
        current_step=2,
        wizard_data=wizard_data
    )

# 路由：向导第三步 - 监考员设置
@app.route('/wizard/step3_proctors', methods=['GET', 'POST'])
@login_required
def wizard_step3_proctors():
    if 'wizard_data' not in session or not session['wizard_data'].get('rooms'):
        flash('请先完成第二步：设置考场。', 'warning')
        return redirect(url_for('wizard_step2_rooms'))

    if request.method == 'POST':
        proctors_data_str = request.form.get('proctors_data')
        if proctors_data_str:
            try:
                proctors = json.loads(proctors_data_str)
                validated_proctors = []
                for p in proctors:
                    if p.get('name'):
                        # Convert empty string for session_limit to None
                        session_limit = p.get('session_limit')
                        if isinstance(session_limit, str) and session_limit.strip() == '':
                            session_limit = None
                        else:
                            try:
                                session_limit = int(session_limit) if session_limit is not None else None
                            except (ValueError, TypeError):
                                session_limit = None # Default to None if conversion fails
                        
                        # 处理考场字段，确保它们是列表格式
                        def ensure_list_format(field_value):
                            if isinstance(field_value, list):
                                return field_value
                            elif isinstance(field_value, str):
                                return [item.strip() for item in field_value.split(',') if item.strip()] if field_value.strip() else []
                            else:
                                return []

                        validated_proctors.append({
                            'name': p.get('name', '').strip(),
                            'teaching_subject': p.get('teaching_subject', '').strip(),
                            'required_subjects': p.get('required_subjects', []),
                            'unavailable_subjects': p.get('unavailable_subjects', []),
                            'required_rooms': ensure_list_format(p.get('required_rooms', [])),
                            'unavailable_rooms': ensure_list_format(p.get('unavailable_rooms', [])),
                            'session_limit': session_limit
                        })
                session['wizard_data']['proctors'] = validated_proctors
                session.modified = True
                return redirect(url_for('wizard_step4_review')) 

            except (json.JSONDecodeError, ValueError):
                flash('提交的数据格式错误', 'danger')

    wizard_data = session.get('wizard_data', {})
    return render_template(
        'wizard/step3_proctors.html',
        title="向导第三步：监考员设置",
        current_step=3,
        wizard_data=wizard_data
    )

# 路由：向导第四步 - 预览与验证
@app.route('/wizard/step4_review', methods=['GET'])
@login_required
def wizard_step4_review():
    if 'wizard_data' not in session or not session['wizard_data'].get('proctors'):
        flash('请先完成第三步：设置监考员。', 'warning')
        return redirect(url_for('wizard_step3_proctors'))

    wizard_data = session.get('wizard_data', {})
    return render_template(
        'wizard/step4_review.html',
        title="向导第四步：预览与验证",
        current_step=4,
        wizard_data=wizard_data,
        now=datetime.now()
    )

# 路由：从向导触发验证
@app.route('/wizard/validate', methods=['POST'])
@login_required
def wizard_validate():
    """
    通过从向导数据生成临时Excel文件来验证配置，
    并对其运行标准验证过程。
    """
    try:
        if 'wizard_data' not in session:
            return jsonify({'error': '向导会话未找到或已过期。'}), 400

        wizard_data = session['wizard_data']
        validation_id = str(uuid.uuid4())
        
        # 创建临时文件路径
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'validation_wizard_{timestamp}_{validation_id}.xlsx'
        file_path = os.path.join(tmp_dir, filename)

        # 从向导数据生成Excel文件
        generate_excel_from_wizard_data(wizard_data, file_path)
        logger.info(f"从向导生成了临时验证文件: {file_path}")

        # 在Redis中初始化验证进度和结果
        set_validation_progress(validation_id, 0)
        initial_result = {
            'is_valid': False,
            'file_path': file_path,
            'errors': [],
            'warnings': [],
            'error_report': None
        }
        store_validation_result(validation_id, initial_result)
        logger.info(f"已使用ID初始化向导数据的验证: {validation_id}")

        # 启动后台验证
        thread = threading.Thread(
            target=validate_file_in_background,
            args=(validation_id, file_path)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'validation_id': validation_id,
            'message': '配置验证已开始。'
        })

    except Exception as e:
        logger.error(f"启动向导验证失败: {str(e)}", exc_info=True)
        return jsonify({'error': '启动验证时发生内部错误。'}), 500

# 路由：创建新任务
@app.route('/task/new', methods=['GET', 'POST'])
@login_required
def new_task():
    # 检查任务数量限制
    if current_user.role != 'admin':
        task_count = current_user.tasks.count()
        if task_count >= current_user.task_limit:
            flash('您已达到任务数量限制，请删除已完成或失败的任务后再创建新任务', 'warning')
            return redirect(url_for('dashboard'))

    if request.method == 'POST':
        try:
        # 获取表单数据
            title = request.form.get('title')
            if not title:
                flash('请输入任务标题')
                return redirect(request.url)

            description = request.form.get('description', '')
            validation_id = request.form.get('validation_id')

            # 如果提供了validation_id，检查验证结果
            if not validation_id:
                flash('请先上传并验证文件', 'error')
                return redirect(request.url)

            result = get_validation_result(validation_id)
            if not result:
                flash('验证结果已过期，请重新上传文件', 'error')
                return redirect(request.url)

            if not result.get('is_valid', False):
                flash('文件验证未通过，请修正错误后重试', 'error')
                return redirect(request.url)

            file_path = result.get('file_path')
            if not file_path or not os.path.exists(file_path):
                flash('验证文件不存在，请重新上传', 'error')
                return redirect(request.url)

            # 创建任务记录
            task = Task(
                title=title,
                description=description,
                user=current_user,
                status='pending'
            )
            db.session.add(task)
            db.session.flush()

            # 创建任务目录
            task_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task.id))
            os.makedirs(task_dir, exist_ok=True)

            # 保存模板文件
            template_filename = f"{task.id}_设置文件.xlsx"
            template_path = os.path.join(task_dir, template_filename)

            # 复制验证通过的文件
            import shutil
            shutil.copy2(file_path, template_path)
            logger.info(f"模板文件已保存到任务目录: {template_path}")

            # 保存为标准输入文件名
            input_filename = f"input_{task.id}.xlsx"
            input_path = os.path.join(task_dir, input_filename)
            shutil.copy2(template_path, input_path)

            # 更新任务记录
            task.input_file = input_path
            task.template_file = template_path
            task.original_filename = os.path.basename(file_path)

            # 提交事务
            db.session.commit()
            logger.info(f"任务 {task.id} 创建成功")

            # 清理验证结果
            cleanup_validation_result(validation_id)

            flash('任务创建成功！')
            return redirect(url_for('task_detail', task_id=task.id))

        except Exception as e:
            logger.error(f"创建任务时发生错误: {str(e)}", exc_info=True)
            if db.session.in_transaction():
                db.session.rollback()
            flash(f'创建任务失败: {str(e)}', 'error')
            return redirect(request.url)

    return render_template('new_task.html', title='新建任务')

# 路由：任务详情
@app.route('/task/<int:task_id>')
@login_required
def task_detail(task_id):
    task = db.session.get(Task, task_id)
    if task is None:
        abort(404)

    # 检查权限
    if task.user_id != current_user.id and current_user.role != 'admin':
        flash('您没有权限查看此任务')
        return redirect(url_for('dashboard'))

    return render_template('task_detail.html', title=task.title, task=task)

# 全局任务进度字典
task_progress_data = {}
task_logs = {}

# 后台任务处理函数
def process_task_in_background(task_id, input_file, output_file):
    with app.app_context():
        task = db.session.get(Task, task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return

        try:
            # 更新任务状态
            task.status = 'processing'
            task.progress = 0
            task.error_message = None
            db.session.commit()

            # 创建适配器实例
            adapter = CoreAdapter(
                task_id=task_id, 
                input_file=input_file, 
                output_file=output_file,
                db=db,
                Task=Task,
                redis_client=redis_client
            )
            
            # 执行处理
            success = adapter.process()
            
            if success:
                task.status = 'completed'
                task.progress = 100
                task.output_file = output_file
            else:
                task.status = 'failed'
                task.progress = 0
                task.error_message = "处理失败，请查看日志了解详情"

            db.session.commit()

        except Exception as e:
            logger.error(f"处理任务 {task_id} 时发生错误: {str(e)}")
            task.status = 'failed'
            task.error_message = str(e)
            db.session.commit()

# 路由：开始处理任务
@app.route('/task/<int:task_id>/schedule', methods=['POST'])
@login_required
def schedule_task(task_id):
    logger.info(f"\n\n开始处理任务 {task_id}")
    logger.info(f"请求方法: {request.method}, 请求数据: {request.form}")
    logger.info(f"请求头: {request.headers}")

    task = db.session.get(Task, task_id)
    if task is None:
        logger.error(f"任务 {task_id} 不存在")
        abort(404)

    # 调用共同的处理函数
    return process_and_schedule_task(task, from_admin=False)

# 路由：管理后台开始处理任务
@app.route('/admin/tasks/<int:task_id>/schedule', methods=['POST', 'GET'])
@login_required
@admin_required
def admin_schedule_task(task_id):
    logger.info(f"\n\n管理后台开始处理任务 {task_id}")
    logger.info(f"请求方法: {request.method}, 请求数据: {request.form}")

    task = db.session.get(Task, task_id)
    if task is None:
        logger.error(f"任务 {task_id} 不存在")
        flash('任务不存在', 'danger')
        return redirect(url_for('admin_tasks'))

    # 检查任务状态
    if task.status != 'pending':
        logger.warning(f"任务 {task_id} 当前状态不是待处理，无法启动")
        flash('只能启动待处理状态的任务', 'warning')
        return redirect(url_for('admin_task_detail', task_id=task.id))

    # 调用共同的处理函数
    return process_and_schedule_task(task, from_admin=True)

# 共同的任务处理函数
def process_and_schedule_task(task, from_admin=False):
    task_id = task.id
    logger.info(f"开始处理任务 {task_id}")

    try:
        # 检查权限和状态
        if task.user_id != current_user.id and current_user.role != 'admin':
            logger.warning(f"用户 {current_user.id} 无权操作任务 {task_id}")
            flash('您没有权限操作此任务', 'error')
            return redirect(url_for('dashboard'))

        if task.status in ['processing', 'completed']:
            logger.warning(f"任务 {task_id} 状态不正确: {task.status}")
            flash('任务已在处理或已完成', 'warning')
            return redirect(url_for('admin_task_detail' if from_admin else 'task_detail', task_id=task.id))

        # 检查文件
        if not task.input_file or not os.path.exists(task.input_file):
            logger.error(f"任务 {task_id} 输入文件不存在: {task.input_file}")
            flash('输入文件不存在', 'error')
            return redirect(url_for('admin_task_detail' if from_admin else 'task_detail', task_id=task.id))

        # 更新任务状态
        task.status = 'processing'
        task.progress = 0
        db.session.commit()

        # 准备输出文件
        task_dir = os.path.dirname(task.input_file)
        safe_title = secure_filename(task.title.strip() or '安排结果')
        if not safe_title or safe_title.isspace():
            safe_title = '安排结果'

        output_filename = f"{task.id}_{safe_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        output_path = os.path.join(task_dir, output_filename)

        # 初始化进度和日志
        task_progress_data[task.id] = 0
        task_logs[task.id] = ['准备开始处理...']

        # 启动后台处理
        thread = threading.Thread(
            target=process_task_in_background,
            args=(task.id, task.input_file, output_path)
        )
        thread.daemon = True
        thread.start()

        flash('任务已开始处理，请稍候...', 'info')
        return redirect(url_for('admin_task_detail' if from_admin else 'task_detail', task_id=task.id))

    except Exception as e:
        logger.error(f"处理任务 {task_id} 时发生错误: {str(e)}", exc_info=True)
        task.status = 'failed'
        task.progress = 0
        task.error_message = str(e)
        db.session.commit()
        flash('处理任务时发生错误', 'error')
        return redirect(url_for('admin_task_detail' if from_admin else 'task_detail', task_id=task.id))

# 路由：下载结果
@app.route('/task/<int:task_id>/download')
@login_required
def download_result(task_id):
    try:
        task = Task.query.get_or_404(task_id)

        # 权限检查
        if task.user_id != current_user.id and current_user.role != 'admin':
            flash('您没有权限下载此任务结果', 'error')
            return redirect(url_for('task_detail', task_id=task.id))

        # 状态检查
        if task.status != 'completed':
            flash('任务尚未完成，无法下载结果', 'warning')
            return redirect(url_for('task_detail', task_id=task.id))

        # 从Redis获取结果文件路径
        result_key = f'task:{task_id}:result_file'
        file_path = redis_client.get(result_key)

        if not file_path:
            # 如果Redis中没有，则从文件系统中查找
            task_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task_id))
            result_files = [f for f in os.listdir(task_dir)
                          if f.endswith('.xlsx')
                          and not f.startswith('input_')
                          and not f.endswith('设置文件.xlsx')]

            if not result_files:
                logger.error(f"任务 {task_id} 的结果文件不存在")
                flash('结果文件不存在', 'error')
                return redirect(url_for('task_detail', task_id=task.id))

            # 获取最新的结果文件
            latest_file = max(result_files, key=lambda x: os.path.getmtime(os.path.join(task_dir, x)))
            file_path = os.path.join(task_dir, latest_file)

            # 将文件路径存入Redis，设置24小时过期
            redis_client.setex(result_key, 86400, file_path)
        else:
            # 处理Redis返回的数据
            if isinstance(file_path, bytes):
                file_path = file_path.decode('utf-8')

        if not os.path.exists(file_path):
            logger.error(f"结果文件不存在: {file_path}")
            # 如果文件不存在，删除Redis中的记录
            redis_client.delete(result_key)
            flash('结果文件不存在', 'error')
            return redirect(url_for('task_detail', task_id=task.id))

        return send_file(
            file_path,
            as_attachment=True,
            download_name=os.path.basename(file_path),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        logger.error(f"下载任务 {task_id} 结果时发生错误: {str(e)}", exc_info=True)
        flash('下载文件时发生错误', 'error')
        return redirect(url_for('task_detail', task_id=task.id))

# 路由：下载原始模板文件
@app.route('/task/<int:task_id>/template')
@login_required
def download_template(task_id):
    try:
        task = Task.query.get_or_404(task_id)

        # 权限检查
        if task.user_id != current_user.id and current_user.role != 'admin':
            flash('您没有权限下载此任务的模板文件', 'error')
            return redirect(url_for('task_detail', task_id=task.id))

        # 从Redis获取模板文件路径
        template_key = f'task:{task_id}:template_file'
        file_path = redis_client.get(template_key)

        if not file_path:
            # 如果Redis中没有，则从文件系统中查找
            task_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task_id))

            # 尝试两种可能的模板文件名称
            template_file_options = [
                os.path.join(task_dir, f"{task_id}_设置文件.xlsx"),  # 正确的命名格式
                os.path.join(task_dir, 'input_设置文件.xlsx')  # 旧的命名格式
            ]

            # 检查所有可能的模板文件
            template_file = None
            for option in template_file_options:
                if os.path.exists(option):
                    template_file = option
                    logger.info(f"找到模板文件: {template_file}")
                    break

            # 如果仍然找不到，尝试直接使用task.template_file
            if not template_file and task.template_file and os.path.exists(task.template_file):
                template_file = task.template_file
                logger.info(f"使用任务记录中的模板文件路径: {template_file}")

            # 如果还是找不到，尝试在目录中查找任何Excel文件
            if not template_file:
                excel_files = [f for f in os.listdir(task_dir) if f.endswith('.xlsx') and '设置文件' in f]
                if excel_files:
                    template_file = os.path.join(task_dir, excel_files[0])
                    logger.info(f"找到可能的模板文件: {template_file}")

            if not template_file or not os.path.exists(template_file):
                logger.error(f"任务 {task_id} 的模板文件不存在")
                flash('模板文件不存在', 'error')
                return redirect(url_for('task_detail', task_id=task.id))

            file_path = template_file
            # 将文件路径存入Redis，设置24小时过期
            redis_client.setex(template_key, 86400, file_path)
        else:
            # 处理Redis返回的数据
            if isinstance(file_path, bytes):
                file_path = file_path.decode('utf-8')

        if not os.path.exists(file_path):
            logger.error(f"模板文件不存在: {file_path}")
            # 如果文件不存在，删除Redis中的记录
            redis_client.delete(template_key)
            flash('模板文件不存在', 'error')
            return redirect(url_for('task_detail', task_id=task.id))

        # 生成更友好的下载文件名
        download_filename = f"{task.title}_设置文件.xlsx"

        return send_file(
            file_path,
            as_attachment=True,
            download_name=download_filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        logger.error(f"下载任务 {task_id} 模板时发生错误: {str(e)}", exc_info=True)
        flash('下载文件时发生错误', 'error')
        return redirect(url_for('task_detail', task_id=task.id))

# 日志脱敏处理函数
def sanitize_logs(logs):
    """
    对任务日志进行脱敏处理，只保留关键的进度信息
    """
    sanitized_logs = []
    progress_keywords = [
        '预处理数据',
        '生成监考安排',
        '导出结果',
        '处理完成',
        '验证失败',
        '开始处理',
        '任务完成',
        '%'
    ]

    for log in logs:
        # 检查是否包含进度关键词
        if any(keyword in log for keyword in progress_keywords):
            # 如果日志中包含文件路径，将其替换为省略号
            sanitized_log = log
            if ':\\' in sanitized_log or '/' in sanitized_log:
                sanitized_log = sanitized_log.split(': ', 1)[-1]
                sanitized_log = sanitized_log.replace('\\', '/').split('/')[-1]
            sanitized_logs.append(sanitized_log)
        # 如果日志包含百分比，保留进度信息
        elif any(str(i) + '%' in log for i in range(101)):
            sanitized_logs.append(log)

    return sanitized_logs

# 路由：获取任务进度
@app.route('/task/<int:task_id>/progress')
@login_required
def get_task_progress(task_id):
    # 获取任务锁，防止并发访问同一任务导致的问题
    task_lock = get_task_lock(task_id)

    # 使用非阻塞锁，如果已经被锁定，直接返回上一次的结果
    if not task_lock.acquire(blocking=False):
        logger.info(f"任务 {task_id} 正在被其他请求处理，返回缓存结果")
        # 尝试从 Redis 获取缓存的进度信息
        try:
            with redis_lock:
                progress_key = f'task:{task_id}:progress'
                progress_data = redis_client.get(progress_key)
                if progress_data:
                    if isinstance(progress_data, bytes):
                        progress_data = progress_data.decode('utf-8')
                    return jsonify(json.loads(progress_data))
        except Exception as e:
            logger.error(f"获取缓存的进度信息时发生错误: {str(e)}")

        # 如果没有缓存的进度信息，返回一个默认的响应
        return jsonify({
            'status': 'processing',
            'progress': 50,  # 默认返回50%进度
            'message': '正在获取最新进度...',
            'logs': ['正在获取最新进度信息...']
        })

    # 如果成功获取锁，确保在函数结束时释放锁
    try:
        # 使用数据库锁保护数据库操作
        with db_lock:
            task = Task.query.get_or_404(task_id)

            # 权限检查
            if task.user_id != current_user.id and current_user.role != 'admin':
                return jsonify({
                    'error': '您没有权限查看此任务的进度'
                }), 403

        # 使用Redis锁保护Redis操作
        with redis_lock:
            # 从Redis获取任务进度
            progress_key = f'task:{task_id}:progress'
            progress_data = redis_client.get(progress_key)

            if progress_data:
                # 处理Redis返回的数据
                if isinstance(progress_data, bytes):
                    progress_data = progress_data.decode('utf-8')
                progress_info = json.loads(progress_data)
            else:
                # 如果Redis中没有进度信息，返回默认值
                with db_lock:
                    progress_info = {
                        'status': task.status,
                        'progress': task.progress,
                        'message': '任务进度信息不可用'
                    }

            # 如果任务已完成，返回100%进度
            with db_lock:
                if task.status in ['completed', 'failed', 'cancelled']:
                    progress_info['progress'] = 100
                    progress_info['status'] = task.status

                    # 清除Redis中的进度信息
                    redis_client.delete(progress_key)

        # 读取进度日志文件
        logs = []
        latest_progress = 0
        progress_log_path = os.path.join(app.config['UPLOAD_FOLDER'], str(task_id), 'logs', 'progress.log')

        # 获取文件锁，防止并发访问同一文件
        file_lock = get_file_lock(progress_log_path)

        if os.path.exists(progress_log_path):
            # 使用文件锁保护文件读取
            with file_lock:
                try:
                    # 尝试使用UTF-8编码读取文件
                    try:
                        with open(progress_log_path, 'r', encoding='utf-8') as f:
                            log_lines = f.readlines()
                    except UnicodeDecodeError:
                        # 如果UTF-8失败，尝试使用系统默认编码
                        logger.warning(f"使用UTF-8读取日志文件失败，尝试使用系统默认编码")
                        with open(progress_log_path, 'r') as f:
                            log_lines = f.readlines()
                    except Exception as e:
                        # 如果仍然失败，尝试使用二进制模式读取并忽略错误
                        logger.warning(f"读取日志文件失败，尝试使用二进制模式: {str(e)}")
                        try:
                            with open(progress_log_path, 'rb') as f:
                                binary_content = f.read()
                                # 尝试使用多种编码解码，忽略错误
                                for encoding in ['utf-8', 'latin-1', 'cp1252', 'gbk']:
                                    try:
                                        decoded_content = binary_content.decode(encoding, errors='replace')
                                        log_lines = decoded_content.splitlines()
                                        logger.info(f"成功使用{encoding}编码读取日志文件")
                                        break
                                    except Exception:
                                        continue
                                else:
                                    # 所有编码都失败，使用替代模式
                                    log_lines = binary_content.decode('utf-8', errors='replace').splitlines()
                                    logger.warning("使用替代模式读取日志文件")
                        except Exception as bin_err:
                            logger.error(f"二进制模式读取日志文件也失败: {str(bin_err)}")
                            log_lines = []

                    # 处理日志行
                    logs = [line.strip() for line in log_lines if line and line.strip()]

                    # 从日志中提取最新的进度百分比
                    import re
                    for line in reversed(logs):
                        if '进度:' in line:
                            progress_match = re.search(r'进度:\s*(\d+)%', line)
                            if progress_match:
                                try:
                                    latest_progress = int(progress_match.group(1))
                                    # 验证进度值是否合理
                                    if 0 <= latest_progress <= 100:
                                        logger.info(f"从日志中提取到进度: {latest_progress}%")
                                        break
                                    else:
                                        logger.warning(f"从日志中提取到的进度值超出范围: {latest_progress}%")
                                except ValueError:
                                    logger.warning(f"无法将进度值转换为整数: {progress_match.group(1)}")

                    # 对日志进行脱敏处理
                    logs = sanitize_logs(logs)
                except Exception as e:
                    logger.error(f"读取任务 {task_id} 的进度日志文件时发生错误: {str(e)}")

        # 如果从日志中提取到了进度，更新progress_info
        if latest_progress > 0:
            progress_info['progress'] = latest_progress
            # 同时更新数据库中的进度
            with db_lock:
                try:
                    # 重新查询任务，确保数据是最新的
                    task = Task.query.get(task_id)
                    if task and task.progress < latest_progress and task.status == 'processing':
                        task.progress = latest_progress
                        db.session.commit()
                        logger.info(f"更新任务 {task_id} 进度为 {latest_progress}%")
                except Exception as db_err:
                    logger.error(f"更新数据库中的进度时发生错误: {str(db_err)}")
                    db.session.rollback()

        # 添加日志到返回数据中
        progress_info['logs'] = logs

        # 添加状态颜色和显示文本
        with db_lock:
            task = Task.query.get(task_id)  # 重新查询任务，确保数据是最新的
            if task:
                progress_info['status_color'] = task.status_color
                progress_info['status_display'] = task.status_display

        # 将最新的进度信息存入Redis
        with redis_lock:
            try:
                if task and task.status == 'processing':
                    redis_client.setex(progress_key, 3600, json.dumps(progress_info))
                    logger.info(f"将任务 {task_id} 的进度信息存入Redis: {progress_info['progress']}%")
            except Exception as e:
                logger.error(f"将进度信息存入Redis时发生错误: {str(e)}")

        return jsonify(progress_info)

    except Exception as e:
        logger.error(f"获取任务 {task_id} 进度时发生错误: {str(e)}", exc_info=True)
        return jsonify({
            'error': '获取任务进度时发生错误'
        }), 500
    finally:
        # 确保释放锁
        task_lock.release()

# 修改定时清理任务
def cleanup_temp_files():
    """清理临时验证文件
    - 删除超过24小时的验证文件
    - 清理Redis中的过期记录
    """
    try:
        current_time = datetime.now()
        cleanup_time = current_time - timedelta(hours=24)  # 24小时过期

        # 清理临时目录中的过期文件
        for filename in os.listdir(tmp_dir):
            if filename.startswith('validation_'):
                file_path = os.path.join(tmp_dir, filename)
                file_time = datetime.fromtimestamp(os.path.getctime(file_path))

                if file_time < cleanup_time:
                    try:
                        os.remove(file_path)
                        logger.info(f"已删除过期的临时文件: {filename}")
                    except Exception as e:
                        logger.error(f"删除临时文件失败 {filename}: {str(e)}")

        # 获取所有验证相关的Redis键
        validation_keys = redis_client.keys("validation_*")
        for key in validation_keys:
            try:
                # 对于progress键，直接删除过期的
                if key.startswith('validation_progress:'):
                    redis_client.delete(key)
                    continue

                # 对于其他验证结果键
                result = redis_client.get(key)
                if result:
                    try:
                        result_data = json.loads(result)
                        if 'expires_at' in result_data:
                            expires_at = datetime.strptime(result_data['expires_at'], '%Y-%m-%d %H:%M:%S.%f')
                            if expires_at < cleanup_time:
                                redis_client.delete(key)
                                logger.info(f"已删除过期的Redis记录: {key}")
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，直接删除
                        redis_client.delete(key)
                        logger.info(f"已删除无效的Redis记录: {key}")
            except Exception as e:
                logger.error(f"处理Redis键 {key} 时发生错误: {str(e)}")

    except Exception as e:
        logger.error(f"清理临时文件时发生错误: {str(e)}")

def cleanup_old_validation_files():
    """清理旧的验证文件"""
    try:
        # 清理临时目录中的旧验证文件
        old_files = [f for f in os.listdir(tmp_dir) if f.startswith('validation_')]
        for old_file in old_files:
            old_file_path = os.path.join(tmp_dir, old_file)
            try:
                os.remove(old_file_path)
                logger.info(f"已删除旧的验证文件: {old_file_path}")
            except Exception as e:
                logger.warning(f"删除旧文件失败 {old_file_path}: {str(e)}")
    except Exception as e:
        logger.warning(f"清理旧文件时出错: {str(e)}")

@app.route('/task/validate', methods=['POST'])
@login_required
def validate_file():
    """处理文件验证请求"""
    try:
        logger.info("开始处理文件验证请求")

        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件类型'}), 400

        # 检查文件大小
        file_content = file.read()
        file_size = len(file_content)
        if file_size > 10 * 1024 * 1024:  # 10MB
            logger.error(f"文件大小超过限制: {file_size} bytes")
            return jsonify({'error': '文件大小不能超过10MB'}), 400
        file.seek(0)  # 重置文件指针

        # 生成唯一的验证ID
        validation_id = str(uuid.uuid4())
        logger.info(f"生成验证ID: {validation_id}")

        # 创建临时目录（如果不存在）
        os.makedirs(tmp_dir, exist_ok=True)

        # 清理旧的验证文件
        cleanup_old_validation_files()

        # 保存文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'validation_{timestamp}_{secure_filename(file.filename)}'
        file_path = os.path.join(tmp_dir, filename)
        logger.info(f"临时文件路径: {file_path}")
        file.save(file_path)
        logger.info(f"验证文件已保存: {file_path}")

        # 初始化验证进度
        set_validation_progress(validation_id, 0)

        # 存储初始验证结果
        initial_result = {
            'is_valid': False,
            'file_path': file_path,
            'errors': [],
            'warnings': [],
            'error_report': None
        }
        store_validation_result(validation_id, initial_result)
        logger.info(f"初始化验证进度和结果: {validation_id}")

        # 启动后台验证线程
        thread = threading.Thread(
            target=validate_file_in_background,
            args=(validation_id, file_path)
        )
        thread.daemon = True
        thread.start()
        logger.info(f"后台验证线程已启动: {validation_id}")

        return jsonify({
            'validation_id': validation_id,
            'message': '文件验证已开始'
        })

    except Exception as e:
        logger.error(f"验证过程发生错误: {str(e)}", exc_info=True)
        return jsonify({'error': '验证过程出错'}), 500

# 设置定时清理任务
def schedule_cleanup():
    """每小时执行一次清理"""
    while True:
        cleanup_temp_files()
        time.sleep(3600)  # 休眠1小时

# 启动清理线程
cleanup_thread = threading.Thread(target=schedule_cleanup, daemon=True)
cleanup_thread.start()

# 创建数据库表
with app.app_context():
    db.create_all()

    # 创建管理员账号（如果不存在）
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            task_limit=999
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()



# 测试日志获取页面
@app.route('/admin/test-logs')
@login_required
@admin_required
def admin_test_logs():
    return render_template('admin/test_logs.html')

# 管理后台路由
@app.route('/admin')
@login_required
@admin_required
def admin_dashboard():
    # 获取基础统计数据
    total_users = User.query.count()
    total_tasks = Task.query.count()
    active_tasks = Task.query.filter_by(status='processing').count()
    completed_tasks = Task.query.filter_by(status='completed').count()

    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_tasks=total_tasks,
                         active_tasks=active_tasks,
                         completed_tasks=completed_tasks)

# 用户管理路由
@app.route('/admin/users')
@login_required
@admin_required
def admin_users():
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(page=page, per_page=10)
    return render_template('admin/users.html', users=users)

@app.route('/admin/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_user_add():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role', 'user')
        is_active = 'is_active' in request.form

        # 根据角色设置默认任务限制
        if role == 'vip':
            task_limit = 6
        elif role == 'admin':
            task_limit = 999
        else:  # 普通用户
            task_limit = 1

        # 如果表单中提供了任务限制，则使用表单中的值
        if request.form.get('task_limit'):
            task_limit = int(request.form.get('task_limit'))

        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在')
            return redirect(url_for('admin_user_add'))

        # 检查邮箱是否已存在
        if User.query.filter_by(email=email).first():
            flash('邮箱已存在')
            return redirect(url_for('admin_user_add'))

        # 创建新用户
        user = User(
            username=username,
            email=email,
            role=role,
            task_limit=task_limit,
            is_active=is_active
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        flash('用户创建成功')
        return redirect(url_for('admin_users'))

    return render_template('admin/user_add.html')

@app.route('/admin/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_user_edit(user_id):
    user = User.query.get_or_404(user_id)

    if request.method == 'POST':
        # 检查用户名是否已被其他用户使用
        new_username = request.form.get('username')
        if new_username != user.username and User.query.filter_by(username=new_username).first():
            flash('用户名已存在')
            return redirect(url_for('admin_user_edit', user_id=user_id))

        # 检查邮箱是否已被其他用户使用
        new_email = request.form.get('email')
        if new_email != user.email and User.query.filter_by(email=new_email).first():
            flash('邮箱已存在')
            return redirect(url_for('admin_user_edit', user_id=user_id))

        # 获取新的角色
        new_role = request.form.get('role', user.role)

        # 处理VIP有效期
        if new_role == 'vip':
            vip_expiry = request.form.get('vip_expiry')
            if vip_expiry:
                # 如果提供了有效期，转换为datetime对象
                user.expiry_date = datetime.strptime(vip_expiry, '%Y-%m-%d')
            elif user.role != 'vip':
                # 如果是从其他角色升级到VIP且没有指定有效期，设置默认一个月
                user.expiry_date = datetime.now() + timedelta(days=30)
        else:
            # 如果不是VIP用户，清除有效期
            user.expiry_date = None

        user.username = new_username
        user.email = new_email
        user.role = new_role
        user.is_active = 'is_active' in request.form
        user.task_limit = int(request.form.get('task_limit', user.task_limit))

        if request.form.get('new_password'):
            user.set_password(request.form.get('new_password'))

        db.session.commit()
        flash('用户信息已更新')
        return redirect(url_for('admin_users'))

    return render_template('admin/user_edit.html', user=user)

@app.route('/admin/users/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_user_delete(user_id):
    user = User.query.get_or_404(user_id)

    # 不允许删除自己
    if user.id == current_user.id:
        flash('不能删除当前登录的用户')
        return redirect(url_for('admin_users'))

    # 删除用户的所有任务
    for task in user.tasks:
        # 删除任务相关的文件
        if task.input_file and os.path.exists(task.input_file):
            os.remove(task.input_file)
        if task.template_file and os.path.exists(task.template_file):
            os.remove(task.template_file)
        if task.output_file and os.path.exists(task.output_file):
            os.remove(task.output_file)

    # 删除用户
    db.session.delete(user)
    db.session.commit()

    flash('用户已删除')
    return redirect(url_for('admin_users'))

# 任务管理路由
@app.route('/admin/tasks')
@login_required
@admin_required
def admin_tasks():
    # 获取筛选参数
    search_query = request.args.get('search', '')
    username = request.args.get('username', '')
    status = request.args.get('status', '')
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 构建基础查询
    query = Task.query.join(User)

    # 应用筛选条件
    if search_query:
        query = query.filter(
            db.or_(
                Task.title.ilike(f'%{search_query}%'),
                Task.description.ilike(f'%{search_query}%')
            )
        )

    if username:
        query = query.filter(User.username.ilike(f'%{username}%'))

    if status and status != 'all':
        query = query.filter(Task.status == status)

    # 按创建时间降序排序
    query = query.order_by(Task.created_at.desc())

    # 执行分页
    tasks = query.paginate(page=page, per_page=per_page)

    # 获取所有可能的状态
    status_choices = [
        ('all', '所有状态'),
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败')
    ]

    return render_template(
        'admin/tasks.html',
        tasks=tasks,
        status_choices=status_choices,
        search_query=search_query,
        username=username,
        status=status
    )

# 批量操作路由
@app.route('/admin/tasks/batch-action', methods=['POST'])
@login_required
@admin_required
def admin_tasks_batch_action():
    try:
        data = request.get_json()
        task_ids = data.get('task_ids', [])
        action = data.get('action')

        if not task_ids:
            return jsonify({'success': False, 'message': '未选择任何任务'}), 400

        tasks = Task.query.filter(Task.id.in_(task_ids)).all()
        logger.info(f"批量操作 {len(tasks)} 个任务, 操作类型: {action}")

        if action == 'delete':
            # 批量删除任务及相关文件
            for task in tasks:
                # 删除任务目录下的所有文件
                task_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task.id))
                if os.path.exists(task_dir):
                    for root, dirs, files in os.walk(task_dir, topdown=False):
                        for file in files:
                            os.remove(os.path.join(root, file))
                        for dir in dirs:
                            os.rmdir(os.path.join(root, dir))
                    os.rmdir(task_dir)
                    logger.info(f"已删除任务 {task.id} 的所有文件和目录")

                # 清理任务相关的进度数据
                if task.id in task_progress_data:
                    del task_progress_data[task.id]
                if task.id in task_logs:
                    del task_logs[task.id]

                # 删除任务记录
                db.session.delete(task)
                logger.info(f"任务 {task.id} 已删除")

            message = '选中的任务已删除'

        elif action == 'reset':
            # 批量重置任务状态
            for task in tasks:
                # 删除输出文件（如果存在）
                if task.output_file and os.path.exists(task.output_file):
                    os.remove(task.output_file)
                    task.output_file = None
                    logger.info(f"已删除任务 {task.id} 的输出文件")

                # 清理日志目录
                log_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task.id), 'logs')
                if os.path.exists(log_dir):
                    for file in os.listdir(log_dir):
                        file_path = os.path.join(log_dir, file)
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                    logger.info(f"已清理任务 {task.id} 的日志文件")

                # 重置任务状态
                task.status = 'pending'
                task.progress = 0
                task.error_message = None
                task.updated_at = datetime.now(timezone.utc)

                # 清理任务相关的进度数据
                if task.id in task_progress_data:
                    del task_progress_data[task.id]
                if task.id in task_logs:
                    del task_logs[task.id]

                logger.info(f"任务 {task.id} 已重置")

            message = '选中的任务已重置'

        else:
            return jsonify({'success': False, 'message': '无效的操作'}), 400

        db.session.commit()
        return jsonify({'success': True, 'message': message})

    except Exception as e:
        db.session.rollback()
        logger.error(f"批量操作任务时发生错误: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/admin/tasks/<int:task_id>')
@login_required
@admin_required
def admin_task_detail(task_id):
    task = db.session.get(Task, task_id)
    if task is None:
        flash('任务不存在', 'danger')
        return redirect(url_for('admin_tasks'))

    # 获取任务目录下的文件列表
    task_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task_id))
    files = []
    if os.path.exists(task_dir):
        for file in os.listdir(task_dir):
            if file != 'logs' and os.path.isfile(os.path.join(task_dir, file)):
                files.append({
                    'name': file,
                    'path': os.path.join(task_dir, file),
                    'size': os.path.getsize(os.path.join(task_dir, file)),
                    'modified': datetime.fromtimestamp(os.path.getmtime(os.path.join(task_dir, file)))
                })

    return render_template('admin/task_detail.html', task=task, files=files)

@app.route('/admin/tasks/<int:task_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_tasks_delete(task_id):
    try:
        task = db.session.get(Task, task_id)
        if task is None:
            return jsonify({'success': False, 'message': '任务不存在'}), 404

        # 删除任务目录下的所有文件
        task_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task_id))
        if os.path.exists(task_dir):
            for root, dirs, files in os.walk(task_dir, topdown=False):
                for file in files:
                    os.remove(os.path.join(root, file))
                for dir in dirs:
                    os.rmdir(os.path.join(root, dir))
            os.rmdir(task_dir)
            logger.info(f"已删除任务 {task_id} 的所有文件和目录")

        # 清理任务相关的进度数据
        if task.id in task_progress_data:
            del task_progress_data[task.id]
        if task.id in task_logs:
            del task_logs[task.id]

        # 删除任务记录
        db.session.delete(task)
        db.session.commit()
        logger.info(f"任务 {task_id} 已删除")

        return jsonify({'success': True, 'message': '任务已删除'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除任务 {task_id} 时发生错误: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

# 系统设置路由
@app.route('/admin/settings', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_settings():
    import sys
    import flask

    if request.method == 'POST':
        app.config['MAX_CONTENT_LENGTH'] = int(request.form.get('max_file_size', 10)) * 1024 * 1024
        flash('系统设置已更新')
        return redirect(url_for('admin_settings'))

    return render_template('admin/settings.html',
                         sys=sys,
                         flask_version=flask.__version__,
                         config=app.config,
                         User=User,
                         Task=Task)

# 批量删除任务
@app.route('/admin/tasks/batch-delete', methods=['POST'])
@login_required
@admin_required
def admin_tasks_batch_delete():
    try:
        data = request.get_json()
        task_ids = data.get('task_ids', [])

        if not task_ids:
            return jsonify({'success': False, 'message': '未选择任何任务'}), 400

        tasks = Task.query.filter(Task.id.in_(task_ids)).all()

        # 删除任务相关的文件
        for task in tasks:
            if task.input_file and os.path.exists(task.input_file):
                os.remove(task.input_file)
            if task.template_file and os.path.exists(task.template_file):
                os.remove(task.template_file)
            if task.output_file and os.path.exists(task.output_file):
                os.remove(task.output_file)

            # 删除任务记录
            db.session.delete(task)

        db.session.commit()
        return jsonify({'success': True, 'message': '删除成功'})

    except Exception as e:
        db.session.rollback()
        logger.error(f"批量删除任务时发生错误: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

# 批量重置任务状态
@app.route('/admin/tasks/batch-reset', methods=['POST'])
@login_required
@admin_required
def admin_tasks_batch_reset():
    try:
        data = request.get_json()
        task_ids = data.get('task_ids', [])

        if not task_ids:
            return jsonify({'success': False, 'message': '未选择任何任务'}), 400

        tasks = Task.query.filter(Task.id.in_(task_ids)).all()

        # 重置任务状态
        for task in tasks:
            task.status = 'pending'
            task.progress = 0
            task.error_message = None

        db.session.commit()
        return jsonify({'success': True, 'message': '状态重置成功'})

    except Exception as e:
        db.session.rollback()
        logger.error(f"批量重置任务状态时发生错误: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/admin/tasks/<int:task_id>/reset', methods=['POST'])
@login_required
@admin_required
def admin_tasks_reset(task_id):
    try:
        task = db.session.get(Task, task_id)
        if task is None:
            return jsonify({'success': False, 'message': '任务不存在'}), 404

        # 删除输出文件（如果存在）
        if task.output_file and os.path.exists(task.output_file):
            os.remove(task.output_file)
            task.output_file = None
            logger.info(f"已删除任务 {task_id} 的输出文件")

        # 清理日志目录
        log_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task_id), 'logs')
        if os.path.exists(log_dir):
            for file in os.listdir(log_dir):
                file_path = os.path.join(log_dir, file)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            logger.info(f"已清理任务 {task_id} 的日志文件")

        # 重置任务状态
        task.status = 'pending'
        task.progress = 0
        task.error_message = None
        task.updated_at = datetime.now(timezone.utc)

        # 清理任务相关的进度数据
        if task.id in task_progress_data:
            del task_progress_data[task.id]
        if task.id in task_logs:
            del task_logs[task.id]

        db.session.commit()
        logger.info(f"任务 {task_id} 已重置")

        return jsonify({'success': True, 'message': '任务已重置'})

    except Exception as e:
        db.session.rollback()
        logger.error(f"重置任务 {task_id} 时发生错误: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

# 路由：删除任务
@app.route('/task/<int:task_id>/delete', methods=['POST'])
@login_required
def delete_task(task_id):
    task = Task.query.get_or_404(task_id)

    # 检查权限
    if task.user_id != current_user.id and current_user.role != 'admin':
        return jsonify({'success': False, 'message': '您没有权限删除此任务'}), 403

    # 只允许删除已完成或失败的任务
    if task.status not in ['completed', 'failed'] and current_user.role != 'admin':
        return jsonify({'success': False, 'message': '只能删除已完成或失败的任务'}), 400

    try:
        # 删除相关文件
        if task.input_file and os.path.exists(task.input_file):
            os.remove(task.input_file)
        if task.template_file and os.path.exists(task.template_file):
            os.remove(task.template_file)
        if task.output_file and os.path.exists(task.output_file):
            os.remove(task.output_file)

        # 删除任务记录
        db.session.delete(task)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '任务已删除'
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"删除任务 {task_id} 时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除任务时发生错误: {str(e)}'
        }), 500

# 路由：更新任务标题
@app.route('/task/<int:task_id>/update', methods=['POST'])
@login_required
def update_task(task_id):
    try:
        task = Task.query.get_or_404(task_id)

        # 检查权限
        if task.user_id != current_user.id and current_user.role != 'admin':
            return jsonify({
                'success': False,
                'message': '您没有权限修改此任务'
            }), 403

        # 获取并验证请求数据
        data = request.get_json()
        if not data or 'title' not in data:
            return jsonify({
                'success': False,
                'message': '请提供任务标题'
            }), 400

        new_title = data['title'].strip()
        if not new_title:
            return jsonify({
                'success': False,
                'message': '任务标题不能为空'
            }), 400

        # 更新任务标题
        task.title = new_title
        task.updated_at = datetime.now(timezone.utc)
        db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '任务标题已更新',
            'title': new_title
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"更新任务 {task_id} 标题时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

# 个人信息相关路由
@app.route('/profile')
@login_required
def profile():
    return render_template('profile.html', title='个人信息')

@app.route('/profile/change-password', methods=['POST'])
@login_required
def change_password():
    try:
        data = request.get_json()
        old_password = data.get('old_password')
        new_password = data.get('new_password')

        # 验证原密码
        if not current_user.check_password(old_password):
            return jsonify({
                'success': False,
                'message': '原密码错误'
            }), 400

        # 更新密码
        current_user.set_password(new_password)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '密码修改成功'
        })

    except Exception as e:
        logger.error(f"修改密码时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': '修改密码失败，请稍后重试'
        }), 500

@app.route('/profile/update-email', methods=['POST'])
@login_required
def update_email():
    try:
        data = request.get_json()
        new_email = data.get('email')

        # 检查邮箱是否已被使用
        if User.query.filter(User.id != current_user.id, User.email == new_email).first():
            return jsonify({
                'success': False,
                'message': '该邮箱已被其他用户使用'
            }), 400

        # 更新邮箱
        current_user.email = new_email
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '邮箱更新成功'
        })

    except Exception as e:
        logger.error(f"更新邮箱时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': '更新邮箱失败，请稍后重试'
        }), 500

# 模板说明页面路由
@app.route('/template-guide')
def template_guide():
    return render_template('template_guide.html', title='模板填写说明')

# Excel导入功能测试路由
@app.route('/test-excel-import')
def test_excel_import():
    """Excel导入功能测试页面"""
    return send_file('test_excel_import.html')

# 下载示例模板
@app.route('/download-example-template')
def download_example_template():
    try:
        template_path = os.path.join(app.root_path, 'template-guide', 'muban.xlsx')
        if not os.path.exists(template_path):
            flash('示例模板文件不存在')
            return redirect(url_for('template_guide'))

        return send_file(
            template_path,
            as_attachment=True,
            download_name='监考安排模板示例.xlsx'
        )

    except Exception as e:
        logger.error(f"下载模板文件时发生错误: {str(e)}")
        flash('下载模板文件失败，请稍后重试')
        return redirect(url_for('template_guide'))


# 向导模板下载路由
@app.route('/wizard/download-template/<template_type>')
@login_required
def wizard_download_template(template_type):
    """下载向导步骤对应的模板文件"""
    try:
        if template_type == 'subjects':
            # 科目模板直接使用静态文件
            template_path = os.path.join(app.root_path, 'template-guide', 'kemu.xlsx')
            download_name = '科目设置模板.xlsx'

            if not os.path.exists(template_path):
                flash('科目模板文件不存在', 'error')
                return redirect(url_for('wizard_start'))

            return send_file(
                template_path,
                as_attachment=True,
                download_name=download_name,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

        elif template_type == 'rooms':
            # 考场模板根据已设置的科目动态生成
            return generate_rooms_template()

        elif template_type == 'proctors':
            # 监考员模板直接使用静态文件
            template_path = os.path.join(app.root_path, 'template-guide', 'jiankaoyuan.xlsx')
            download_name = '监考员设置模板.xlsx'

            if not os.path.exists(template_path):
                flash('监考员模板文件不存在', 'error')
                return redirect(url_for('wizard_start'))

            return send_file(
                template_path,
                as_attachment=True,
                download_name=download_name,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        else:
            flash('无效的模板类型', 'error')
            return redirect(url_for('wizard_start'))

    except Exception as e:
        logger.error(f"下载模板文件时发生错误: {str(e)}")
        flash('下载模板文件失败，请稍后重试', 'error')
        return redirect(url_for('wizard_start'))

def generate_rooms_template():
    """根据第一步设置的科目动态生成考场模板"""
    import pandas as pd
    import io

    try:
        # 检查是否有向导数据和科目设置
        if 'wizard_data' not in session or not session['wizard_data'].get('subjects'):
            flash('请先完成第一步：设置考试科目', 'warning')
            return redirect(url_for('wizard_step1_subjects'))

        # 获取已设置的科目
        subjects = session['wizard_data']['subjects']
        subject_names = [subject['subject_name'] for subject in subjects]

        # 创建考场模板数据
        template_data = []

        # 添加表头
        header = ['考场'] + subject_names
        template_data.append(header)

        # 添加示例数据行
        example_rooms = ['1考场', '2考场', '3考场', '4考场', '5考场']
        for room_name in example_rooms:
            row = [room_name] + [2] * len(subject_names)  # 每个科目默认需要2个监考员
            template_data.append(row)

        # 创建DataFrame
        df = pd.DataFrame(template_data[1:], columns=template_data[0])

        # 创建Excel文件到内存
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='考场设置', index=False)

            # 获取工作簿和工作表
            workbook = writer.book
            worksheet = writer.sheets['考场设置']

            # 设置列宽
            worksheet.column_dimensions['A'].width = 15  # 考场列
            for i, subject_name in enumerate(subject_names, start=2):
                col_letter = chr(64 + i)  # B, C, D, ...
                worksheet.column_dimensions[col_letter].width = 12

        output.seek(0)

        # 生成下载文件名
        subject_list = '_'.join(subject_names[:3])  # 最多显示前3个科目名
        if len(subject_names) > 3:
            subject_list += f'等{len(subject_names)}科'
        download_name = f'考场设置模板_{subject_list}.xlsx'

        return send_file(
            output,
            as_attachment=True,
            download_name=download_name,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        logger.error(f"生成考场模板时发生错误: {str(e)}")
        flash('生成考场模板失败，请稍后重试', 'error')
        return redirect(url_for('wizard_step2_rooms'))

# 向导Excel导入路由
@app.route('/wizard/import-excel/<step_type>', methods=['POST'])
@login_required
def wizard_import_excel(step_type):
    """导入Excel文件到向导步骤"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'message': '不支持的文件类型，请上传Excel文件'}), 400
        
        # 检查文件大小
        file_content = file.read()
        file_size = len(file_content)
        if file_size > 5 * 1024 * 1024:  # 5MB
            return jsonify({'success': False, 'message': '文件大小不能超过5MB'}), 400
        file.seek(0)  # 重置文件指针
        
        # 保存临时文件
        temp_filename = f'temp_import_{uuid.uuid4().hex}.xlsx'
        temp_path = os.path.join(tmp_dir, temp_filename)
        file.save(temp_path)
        
        try:
            # 根据步骤类型导入数据
            if step_type == 'subjects':
                imported_data = import_subjects_from_excel(temp_path)
            elif step_type == 'rooms':
                imported_data = import_rooms_from_excel(temp_path)
            elif step_type == 'proctors':
                imported_data = import_proctors_from_excel(temp_path)
            else:
                return jsonify({'success': False, 'message': '无效的导入类型'}), 400
            
            return jsonify({
                'success': True,
                'message': f'成功导入{len(imported_data)}条数据',
                'data': imported_data
            })
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
                
    except Exception as e:
        logger.error(f"导入Excel文件时发生错误: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'导入文件时发生错误: {str(e)}'}), 500

# Excel导入处理函数
def import_subjects_from_excel(file_path):
    """从Excel文件导入科目数据"""
    import pandas as pd
    
    try:
        # 尝试读取不同的工作簿名称
        possible_sheet_names = ['考试科目设置', 'Sheet1', '科目设置', '科目']
        df = None
        
        for sheet_name in possible_sheet_names:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                logger.info(f"成功读取工作簿: {sheet_name}")
                break
            except:
                continue
        
        if df is None:
            # 如果没有找到指定工作簿，读取第一个工作簿
            df = pd.read_excel(file_path)
            logger.info("读取第一个工作簿")
        
        # 清理列名
        df.columns = df.columns.str.strip()
        
        # 检查必需的列
        required_columns = ['课程代码', '课程名称', '开始时间', '结束时间']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"缺少必需的列: {', '.join(missing_columns)}")
        
        subjects = []
        for _, row in df.iterrows():
            if pd.isna(row['课程代码']) or pd.isna(row['课程名称']):
                continue
                
            try:
                # 处理时间格式
                start_time = pd.to_datetime(row['开始时间'])
                end_time = pd.to_datetime(row['结束时间'])
                
                subjects.append({
                    'subject_code': str(row['课程代码']).strip(),
                    'subject_name': str(row['课程名称']).strip(),
                    'start_time': start_time.strftime('%Y/%m/%d %H:%M'),
                    'end_time': end_time.strftime('%Y/%m/%d %H:%M')
                })
            except Exception as e:
                logger.warning(f"跳过无效的科目行: {e}")
                continue
        
        return subjects
        
    except Exception as e:
        logger.error(f"解析科目Excel文件时发生错误: {str(e)}")
        raise ValueError(f"解析文件失败: {str(e)}")

def import_rooms_from_excel(file_path):
    """从Excel文件导入考场数据"""
    import pandas as pd
    
    try:
        # 尝试读取不同的工作簿名称
        possible_sheet_names = ['考场设置', 'Sheet1', '考场', '考场需求']
        df = None
        
        for sheet_name in possible_sheet_names:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                logger.info(f"成功读取工作簿: {sheet_name}")
                break
            except:
                continue
        
        if df is None:
            df = pd.read_excel(file_path)
            logger.info("读取第一个工作簿")
        
        # 清理列名
        df.columns = df.columns.str.strip()
        
        # 检查考场列
        if '考场' not in df.columns:
            raise ValueError("缺少必需的列: 考场")
        
        rooms = []
        for _, row in df.iterrows():
            if pd.isna(row['考场']):
                continue
                
            room_name = str(row['考场']).strip()
            demands = {}
            
            # 获取除考场列之外的所有列作为科目需求
            for col in df.columns:
                if col != '考场' and not pd.isna(row[col]):
                    try:
                        demands[col] = int(float(row[col]))
                    except:
                        demands[col] = 0
            
            rooms.append({
                'name': room_name,
                'demands': demands
            })
        
        return rooms
        
    except Exception as e:
        logger.error(f"解析考场Excel文件时发生错误: {str(e)}")
        raise ValueError(f"解析文件失败: {str(e)}")

def import_proctors_from_excel(file_path):
    """从Excel文件导入监考员数据"""
    import pandas as pd
    
    try:
        # 尝试读取不同的工作簿名称
        possible_sheet_names = ['监考员设置', 'Sheet1', '监考员', '监考老师']
        df = None
        
        for sheet_name in possible_sheet_names:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                logger.info(f"成功读取工作簿: {sheet_name}")
                break
            except:
                continue
        
        if df is None:
            df = pd.read_excel(file_path)
            logger.info("读取第一个工作簿")
        
        # 清理列名
        df.columns = df.columns.str.strip()
        
        # 检查必需的列 - 监考老师是必需的
        if '监考老师' not in df.columns:
            raise ValueError("缺少必需的列: 监考老师")
        
        proctors = []
        for _, row in df.iterrows():
            if pd.isna(row['监考老师']):
                continue
                
            # 处理场次限制
            session_limit = None
            if '场次限制' in df.columns and not pd.isna(row['场次限制']):
                try:
                    session_limit = int(float(row['场次限制']))
                except:
                    session_limit = None
            
            # 处理列表类型的字段
            def process_list_field(field_name):
                if field_name in df.columns and not pd.isna(row[field_name]):
                    value = str(row[field_name]).strip()
                    # 检查是否为有效值（排除'nan'字符串）
                    if value and value.lower() != 'nan':
                        # 支持多种分隔符：。，,;|: \t\n.-_/\\()[]{}<>*&^%$#@!~`+=?
                        import re
                        # 使用正则表达式分割，支持多种分隔符
                        separators = r'[。，,;|:\s\t\n.\-_/\\()\[\]{}<>*&^%$#@!~`+=?]+'
                        items = re.split(separators, value)
                        # 过滤空字符串并去除首尾空格
                        return [item.strip() for item in items if item.strip()]
                return []
            
            proctors.append({
                'name': str(row['监考老师']).strip(),
                'teaching_subject': str(row.get('任教科目', '')).strip() if not pd.isna(row.get('任教科目', '')) else '',
                'required_subjects': process_list_field('必监考科目'),
                'unavailable_subjects': process_list_field('不监考科目'),
                'required_rooms': process_list_field('必监考考场'),
                'unavailable_rooms': process_list_field('不监考考场'),
                'session_limit': session_limit
            })
        
        return proctors
        
    except Exception as e:
        logger.error(f"解析监考员Excel文件时发生错误: {str(e)}")
        raise ValueError(f"解析文件失败: {str(e)}")

# 后台验证函数
def validate_file_in_background(validation_id, file_path):
    try:
        # 更新进度的回调函数
        def update_progress(progress, message):
            set_validation_progress(validation_id, progress)
            logger.info(f"验证进度 {validation_id}: {progress}% - {message}")

        # 创建验证器并执行验证
        validator = ExtendedExcelValidator(
            file_path=file_path,
            progress_callback=update_progress
        )

        try:
            # 执行验证
            is_valid = validator.validate()
            validation_messages = validator.get_validation_messages()

            # 处理验证结果
            result = {
                'is_valid': is_valid,
                'file_path': file_path,
                'errors': validation_messages.get('errors', []),
                'warnings': validation_messages.get('warnings', []),
                'error_report': validation_messages.get('error_report')
            }

            # 存储验证结果
            store_validation_result(validation_id, result)

        except Exception as e:
            logger.error(f"验证过程发生错误: {str(e)}", exc_info=True)
            error_result = {
                'is_valid': False,
                'file_path': file_path,
                'errors': [f"验证过程发生致命错误: {str(e)}"],
                'warnings': [],
                'error_report': None
            }
            store_validation_result(validation_id, error_result)

        finally:
            # 确保验证器资源被释放
            try:
                validator.close()
            except Exception as close_error:
                logger.error(f"关闭验证器时发生错误: {str(close_error)}")

            # 确保进度达到100%
            set_validation_progress(validation_id, 100)

    except Exception as e:
        logger.error(f"验证线程发生错误: {str(e)}", exc_info=True)
        error_result = {
            'is_valid': False,
            'file_path': file_path,
            'errors': [f"验证过程发生致命错误: {str(e)}"],
            'warnings': [],
            'error_report': None
        }
        store_validation_result(validation_id, error_result)

@app.route('/task/validate/<validation_id>/progress', methods=['GET'])
@login_required
def get_validation_progress_route(validation_id):
    """获取验证进度"""
    try:
        logger.info(f"检查验证进度: {validation_id}")
        progress = get_validation_progress(validation_id)
        logger.info(f"验证进度: {progress}")

        if progress == 100:
            # 如果验证完成，返回完整结果
            result = get_validation_result(validation_id)
            if result:
                logger.info("返回完整验证结果: " + str(result))
                # 将validation_id添加到结果负载中，供前端使用
                result['validation_id'] = validation_id
                return jsonify({
                    'progress': progress,
                    'completed': True,
                    'result': result
                })

        return jsonify({
            'progress': progress,
            'completed': False
        })

    except Exception as e:
        logger.error(f"获取验证进度时发生错误: {str(e)}", exc_info=True)
        return jsonify({'error': '获取进度失败'}), 500

@app.route('/task/create-from-validated/<validation_id>', methods=['POST'])
@login_required
def create_task_from_validated(validation_id):
    """从验证通过的文件创建任务"""
    try:
        # 检查验证结果是否有效
        if not is_validation_result_valid(validation_id):
            return jsonify({
                'success': False,
                'message': '验证结果已过期，请重新上传文件'
            }), 400

        # 获取验证结果
        result = get_validation_result(validation_id)
        if not result:
            return jsonify({
                'success': False,
                'message': '找不到验证结果'
            }), 404

        if not result.get('is_valid', False):
            return jsonify({
                'success': False,
                'message': '文件验证未通过，请修正错误后重试'
            }), 400

        # 获取表单数据
        title = request.form.get('title', '').strip()
        description = request.form.get('description', '').strip()

        if not title:
            return jsonify({
                'success': False,
                'message': '请输入任务标题'
            }), 400

        # 检查任务数量限制
        if current_user.role != 'admin':
            task_count = current_user.tasks.count()
            if task_count >= current_user.task_limit:
                return jsonify({
                    'success': False,
                    'message': '您已达到任务数量限制'
                }), 400

        # 创建任务记录
        task = Task(
            title=title,
            description=description,
            user=current_user,
            status='pending'
        )

        # 设置任务优先级
        task.set_priority_by_user()

        db.session.add(task)
        db.session.flush()  # 获取task.id

        # 创建任务目录
        task_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task.id))
        os.makedirs(task_dir, exist_ok=True)

        # 复制验证通过的文件
        validated_file = result.get('file_path')
        if not validated_file or not os.path.exists(validated_file):
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': '验证文件不存在'
            }), 400

        # 保存为标准文件名
        template_filename = f"{task.id}_设置文件.xlsx"
        template_path = os.path.join(task_dir, template_filename)
        input_filename = f"input_{task.id}.xlsx"
        input_path = os.path.join(task_dir, input_filename)

        import shutil
        shutil.copy2(validated_file, template_path)
        shutil.copy2(validated_file, input_path)

        # 更新任务记录
        task.input_file = input_path
        task.template_file = template_path
        task.original_filename = os.path.basename(validated_file)

        # 提交事务
        db.session.commit()

        # 清理验证结果
        cleanup_validation_result(validation_id)

        # 如果存在，则清理向导会话数据
        if 'wizard_id' in session:
            session.pop('wizard_data', None)
            session.pop('wizard_id', None)

        # 记录任务创建日志
        logger.info(f"用户 {current_user.username}({current_user.role}) 创建了优先级为 {task.priority} 的任务 {task.id}")

        return jsonify({
            'success': True,
            'message': '任务创建成功',
            'task_id': task.id
        })

    except Exception as e:
        logger.error(f"创建任务时发生错误: {str(e)}", exc_info=True)
        if db.session.in_transaction():
            db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建任务时发生错误: {str(e)}'
        }), 500

def get_queue_status():
    """获取任务队列状态"""
    try:
        with queue_lock:
            return {
                'processing_count': len(processing_tasks),
                'pending_count': task_queue.qsize(),
                'max_workers': MAX_WORKERS
            }
    except Exception as e:
        logger.error(f"获取队列状态时发生错误: {str(e)}", exc_info=True)
        return {
            'processing_count': 0,
            'pending_count': 0,
            'max_workers': MAX_WORKERS
        }

def can_accept_new_task():
    """检查是否可以接受新任务"""
    try:
        status = get_queue_status()
        total_tasks = status['processing_count'] + status['pending_count']
        return total_tasks < MAX_WORKERS * 2  # 允许等待的任务数是工作线程数的2倍
    except Exception as e:
        logger.error(f"检查任务接受状态时发生错误: {str(e)}", exc_info=True)
        return False

def process_task_queue():
    """处理任务队列"""
    while True:
        try:
            # 从队列获取任务
            priority, task_id = task_queue.get()

            try:
                # 更新任务状态为处理中
                with app.app_context():
                    task = Task.query.get(task_id)
                    if not task:
                        logger.error(f"找不到任务 {task_id}")
                        continue

                    if task.status != 'pending':
                        logger.warning(f"任务 {task_id} 状态不是 pending，跳过处理")
                        continue

                    task.status = 'processing'
                    db.session.commit()

                # 添加到处理中的任务集合
                with queue_lock:
                    processing_tasks.add(task_id)

                # 准备输出文件
                output_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(task_id))
                os.makedirs(output_dir, exist_ok=True)

                # 提交任务到线程池
                future = task_executor.submit(process_task_in_background,
                                            task_id,
                                            task.input_file,
                                            os.path.join(output_dir, f"output_{task_id}.xlsx"))
                future.add_done_callback(lambda f: handle_task_completion(task_id, f))

            except Exception as e:
                logger.error(f"处理任务 {task_id} 时发生错误: {str(e)}", exc_info=True)
                with app.app_context():
                    task = Task.query.get(task_id)
                    if task:
                        task.status = 'failed'
                        task.error_message = str(e)
                        db.session.commit()

                with queue_lock:
                    if task_id in processing_tasks:
                        processing_tasks.remove(task_id)
            finally:
                task_queue.task_done()

        except Exception as e:
            logger.error(f"任务队列处理器发生错误: {str(e)}", exc_info=True)
            continue

def handle_task_completion(task_id, future):
    """处理任务完成回调"""
    try:
        # 从处理中的任务集合移除
        with queue_lock:
            if task_id in processing_tasks:
                processing_tasks.remove(task_id)

        # 获取任务结果
        result = future.result()

        # 更新任务状态
        with app.app_context():
            task = Task.query.get(task_id)
            if task:
                if result.get('success'):
                    task.status = 'completed'
                    task.result = result.get('data')
                else:
                    task.status = 'failed'
                    task.error_message = result.get('error')
                db.session.commit()

    except Exception as e:
        logger.error(f"处理任务 {task_id} 完成回调时发生错误: {str(e)}", exc_info=True)
        with app.app_context():
            task = Task.query.get(task_id)
            if task:
                task.status = 'failed'
                task.error_message = str(e)
                db.session.commit()

# 启动任务队列处理线程
queue_processor = threading.Thread(target=process_task_queue, daemon=True)
queue_processor.start()

# 健康检查路由
@app.route('/health')
def health_check():
    """系统健康检查接口，用于监控和负载均衡器检查"""
    # 导入SQLAlchemy的text函数
    from sqlalchemy import text

    health_status = {
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'components': {}
    }

    # 检查数据库连接
    try:
        db.session.execute(text('SELECT 1'))
        health_status['components']['database'] = {'status': 'ok'}
    except Exception as e:
        health_status['status'] = 'error'
        health_status['components']['database'] = {
            'status': 'error',
            'message': str(e)
        }

    # 检查Redis连接
    try:
        redis_client.ping()
        health_status['components']['redis'] = {'status': 'ok'}
    except Exception as e:
        health_status['status'] = 'error'
        health_status['components']['redis'] = {
            'status': 'error',
            'message': str(e)
        }

    # 检查文件系统
    try:
        upload_dir = app.config['UPLOAD_FOLDER']
        if os.path.exists(upload_dir) and os.access(upload_dir, os.W_OK):
            health_status['components']['filesystem'] = {'status': 'ok'}
        else:
            health_status['status'] = 'error'
            health_status['components']['filesystem'] = {
                'status': 'error',
                'message': f'上传目录 {upload_dir} 不存在或无写入权限'
            }
    except Exception as e:
        health_status['status'] = 'error'
        health_status['components']['filesystem'] = {
            'status': 'error',
            'message': str(e)
        }

    # 检查线程池状态
    try:
        health_status['components']['thread_pool'] = {
            'status': 'ok',
            'active_threads': len(processing_tasks),
            'max_workers': MAX_WORKERS
        }
    except Exception as e:
        health_status['status'] = 'error'
        health_status['components']['thread_pool'] = {
            'status': 'error',
            'message': str(e)
        }

    # 检查系统资源
    try:
        import psutil
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        health_status['components']['system'] = {
            'status': 'ok',
            'memory_percent': memory.percent,
            'disk_percent': disk.percent,
            'cpu_percent': psutil.cpu_percent(interval=0.1)
        }

        # 如果资源使用过高，设置警告状态
        if memory.percent > 90 or disk.percent > 90 or psutil.cpu_percent(interval=0.1) > 90:
            health_status['status'] = 'warning'
            health_status['components']['system']['status'] = 'warning'
    except ImportError:
        health_status['components']['system'] = {
            'status': 'unknown',
            'message': 'psutil模块未安装'
        }
    except Exception as e:
        health_status['components']['system'] = {
            'status': 'error',
            'message': str(e)
        }

    # 返回健康状态
    status_code = 200 if health_status['status'] == 'ok' else 500
    return jsonify(health_status), status_code

# 系统状态路由，需要管理员权限
@app.route('/admin/system-status')
@login_required
@admin_required
def system_status():
    """系统状态页面，显示详细的系统运行状态"""
    return render_template('admin/system_status.html', title='系统状态')

# 获取系统状态数据的API
@app.route('/admin/api/system-status')
@login_required
@admin_required
def system_status_api():
    """获取系统状态数据的API"""
    try:
        import psutil
        # 获取系统资源使用情况
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        cpu_percent = psutil.cpu_percent(interval=0.5)

        # 获取活跃任务数量
        with db_lock:
            active_tasks = Task.query.filter_by(status='processing').count()
            pending_tasks = Task.query.filter_by(status='pending').count()
            completed_tasks = Task.query.filter_by(status='completed').count()
            failed_tasks = Task.query.filter_by(status='failed').count()

        # 获取线程池状态
        with queue_lock:
            active_threads = len(processing_tasks)
            queue_size = task_queue.qsize()

        # 获取Redis状态
        try:
            redis_info = redis_client.info()
            redis_status = {
                'connected_clients': redis_info.get('connected_clients', 0),
                'used_memory_human': redis_info.get('used_memory_human', 'unknown'),
                'status': 'ok'
            }
        except Exception as e:
            redis_status = {
                'status': 'error',
                'message': str(e)
            }

        # 获取文件系统状态
        upload_dir = app.config['UPLOAD_FOLDER']
        upload_dir_size = 0
        for dirpath, dirnames, filenames in os.walk(upload_dir):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                upload_dir_size += os.path.getsize(fp)

        # 返回系统状态数据
        return jsonify({
            'system': {
                'cpu_percent': cpu_percent,
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used,
                    'free': memory.free
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': disk.percent
                }
            },
            'application': {
                'tasks': {
                    'active': active_tasks,
                    'pending': pending_tasks,
                    'completed': completed_tasks,
                    'failed': failed_tasks,
                    'total': active_tasks + pending_tasks + completed_tasks + failed_tasks
                },
                'thread_pool': {
                    'active_threads': active_threads,
                    'max_workers': MAX_WORKERS,
                    'queue_size': queue_size
                },
                'redis': redis_status,
                'filesystem': {
                    'upload_dir_size': upload_dir_size,
                    'upload_dir_size_human': f'{upload_dir_size / (1024*1024):.2f} MB'
                }
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取系统状态数据时发生错误: {str(e)}", exc_info=True)
        return jsonify({
            'error': '获取系统状态数据时发生错误',
            'message': str(e)
        }), 500

def generate_excel_from_wizard_data(wizard_data, file_path):
    """根据向导数据生成符合监考安排.xlsx格式的Excel文件"""
    from datetime import datetime
    import pandas as pd

    # 1. 考试科目设置表 - 生成同时符合验证器和core程序要求的格式
    subjects_data = []
    for subject in wizard_data.get('subjects', []):
        start_time_str = subject.get('start_time', '')
        end_time_str = subject.get('end_time', '')

        # 解析时间字符串
        start_datetime = None
        end_datetime = None

        try:
            # 优先处理新格式的时间字符串 "YYYY/MM/DD HH:MM"
            if start_time_str:
                start_time_str = start_time_str.strip()
                logger.info(f"解析开始时间: {start_time_str}")

                # 支持的时间格式
                time_formats = [
                    '%Y/%m/%d %H:%M',      # 2025/02/08 09:00 (新格式)
                    '%Y-%m-%d %H:%M',      # 2025-02-08 09:00
                    '%Y/%m/%d %H:%M:%S',   # 2025/02/08 09:00:00
                    '%Y-%m-%d %H:%M:%S',   # 2025-02-08 09:00:00
                ]
                
                for fmt in time_formats:
                    try:
                        start_datetime = datetime.strptime(start_time_str, fmt)
                        logger.info(f"成功解析开始时间，格式: {fmt}, 结果: {start_datetime}")
                        break
                    except ValueError:
                        continue

            if end_time_str:
                end_time_str = end_time_str.strip()
                logger.info(f"解析结束时间: {end_time_str}")

                # 支持的时间格式
                time_formats = [
                    '%Y/%m/%d %H:%M',      # 2025/02/08 11:00 (新格式)
                    '%Y-%m-%d %H:%M',      # 2025-02-08 11:00
                    '%Y/%m/%d %H:%M:%S',   # 2025/02/08 11:00:00
                    '%Y-%m-%d %H:%M:%S',   # 2025-02-08 11:00:00
                ]
                
                for fmt in time_formats:
                    try:
                        end_datetime = datetime.strptime(end_time_str, fmt)
                        logger.info(f"成功解析结束时间，格式: {fmt}, 结果: {end_datetime}")
                        break
                    except ValueError:
                        continue

        except Exception as e:
            logger.error(f"解析时间时出错: {e}")

        # 如果解析失败，尝试兼容旧格式
        if not start_datetime or not end_datetime:
            logger.warning("使用新格式解析失败，尝试兼容旧格式")
            try:
                # 兼容旧格式：分别的日期和时间字段
                exam_date = subject.get('exam_date', '')
                start_time_only = subject.get('start_time', '') if not start_datetime else None
                end_time_only = subject.get('end_time', '') if not end_datetime else None
                
                if exam_date and start_time_only and not start_datetime:
                    combined_start = f"{exam_date} {start_time_only}"
                    for fmt in ['%Y-%m-%d %H:%M', '%Y/%m/%d %H:%M']:
                        try:
                            start_datetime = datetime.strptime(combined_start, fmt)
                            logger.info(f"兼容模式解析开始时间成功: {start_datetime}")
                            break
                        except ValueError:
                            continue
                
                if exam_date and end_time_only and not end_datetime:
                    combined_end = f"{exam_date} {end_time_only}"
                    for fmt in ['%Y-%m-%d %H:%M', '%Y/%m/%d %H:%M']:
                        try:
                            end_datetime = datetime.strptime(combined_end, fmt)
                            logger.info(f"兼容模式解析结束时间成功: {end_datetime}")
                            break
                        except ValueError:
                            continue
            except Exception as e:
                logger.error(f"兼容模式解析时间失败: {e}")

        # 如果仍然解析失败，使用默认值
        if not start_datetime:
            start_datetime = datetime(2025, 2, 8, 9, 0)
            logger.warning(f"使用默认开始时间: {start_datetime}")
        if not end_datetime:
            end_datetime = datetime(2025, 2, 8, 11, 0)
            logger.warning(f"使用默认结束时间: {end_datetime}")

        # 生成Excel数据行 - 按照用户要求的格式：课程代码、课程名称、开始时间、结束时间
        subjects_data.append({
            '课程代码': subject.get('subject_code', '').strip(),
            '课程名称': subject.get('subject_name', '').strip(),
            '开始时间': start_datetime.strftime('%Y/%m/%d %H:%M'),  # yyyy/mm/dd hh:mm 格式
            '结束时间': end_datetime.strftime('%Y/%m/%d %H:%M')    # yyyy/mm/dd hh:mm 格式
        })

    # 创建DataFrame并保存为Excel
    df_subjects = pd.DataFrame(subjects_data)

    # 创建Excel文件
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        # 写入考试科目设置表
        df_subjects.to_excel(writer, sheet_name='考试科目设置', index=False)

        # 2. 监考员设置表 - 包含验证器和core程序要求的所有列
        proctors_data = []
        for i, proctor in enumerate(wizard_data.get('proctors', []), 1):
            session_limit = proctor.get('session_limit')
            # 确保场次限制是有效的整数
            try:
                session_limit = int(session_limit) if session_limit is not None else 99
                if session_limit <= 0:
                    session_limit = 99
            except (ValueError, TypeError):
                session_limit = 99

            # 处理列表字段，转换为逗号分隔的字符串，不包含[]符号
            def format_list_field(field_value):
                """将列表字段转换为逗号分隔的字符串"""
                if field_value is None or field_value == '' or field_value == []:
                    return ''
                if isinstance(field_value, list):
                    # 过滤空字符串，只保留有效的科目/考场名称
                    valid_items = [str(item).strip() for item in field_value if str(item).strip()]
                    return ', '.join(valid_items) if valid_items else ''
                elif isinstance(field_value, str):
                    stripped = field_value.strip()
                    return stripped if stripped else ''
                else:
                    str_value = str(field_value).strip()
                    return str_value if str_value and str_value.lower() != 'nan' else ''

            proctors_data.append({
                '序号': i,                                    # 验证器和core程序要求的序号列
                '监考老师': proctor.get('name', '').strip(),  # core程序要求的监考老师列
                '任教科目': proctor.get('teaching_subject', ''),  # 验证器和core程序要求的任教科目列
                '必监考科目': format_list_field(proctor.get('required_subjects', '')),  # 转换为逗号分隔字符串
                '不监考科目': format_list_field(proctor.get('unavailable_subjects', '')),  # 转换为逗号分隔字符串
                '必监考考场': format_list_field(proctor.get('required_rooms', '')),     # 转换为逗号分隔字符串
                '不监考考场': format_list_field(proctor.get('unavailable_rooms', '')),   # 转换为逗号分隔字符串
                '场次限制': session_limit                     # 验证器和core程序要求的场次限制列
            })

        df_proctors = pd.DataFrame(proctors_data)
        # 将空字符串替换为实际的空字符串，避免pandas转换为NaN
        df_proctors = df_proctors.fillna('')
        df_proctors.to_excel(writer, sheet_name='监考员设置', index=False)

        # 3. 考场设置表
        rooms_data = []
        subject_names = [s['subject_name'].strip() for s in wizard_data.get('subjects', [])]

        for room in wizard_data.get('rooms', []):
            room_data = {'考场': room.get('name', '').strip()}
            demands = room.get('demands', {})

            for subj in subject_names:
                # 确保需求是有效的整数
                try:
                    demand = int(demands.get(subj, 0))
                    if demand < 0:
                        demand = 0
                except (ValueError, TypeError):
                    demand = 0
                room_data[subj] = demand

            rooms_data.append(room_data)

        df_rooms = pd.DataFrame(rooms_data)
        df_rooms.to_excel(writer, sheet_name='考场设置', index=False)

    logger.info(f"成功从向导数据生成Excel文件: {file_path}")

if __name__ == '__main__':
    app.run(debug=True)
